package main

import (
	"context"
	"log"
	"net/http"

	"github.com/gin-gonic/gin"

	"pxpat-backend/internal/user-cluster/gateway/config"
	"pxpat-backend/internal/user-cluster/gateway/router"
	"pxpat-backend/internal/user-cluster/gateway/service"
	"pxpat-backend/pkg/consul"
)

// @title User Community Cluster Gateway API
// @version 1.0
// @description 用户与社区集群网关服务
// @BasePath /api

func main() {
	// 1. 加载网关配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("加载网关配置失败: %v", err)
	}

	// 2. 初始化服务管理器(负责管理集群内服务)
	svcManager := service.NewServiceManager(cfg)

	// 3. 启动内部服务连接
	if err := svcManager.StartServices(); err != nil {
		log.Fatalf("连接内部服务失败: %v", err)
	}

	// 4. 设置网关路由
	r := gin.Default()
	// TODO 设置CORS
	//r.Use(cors.CORSMiddleware(cors.CorsStruct{
	//	Origin:      "*",
	//	Credentials: "true",
	//	Headers:     "*",
	//	Methods:     "*",
	//}))

	// 注册API路由
	router.RegisterRoutes(r, svcManager)

	// 初始化Consul管理器
	consulManager, err := consul.NewManager(&cfg.Consul)
	if err != nil {
		log.Fatalf("Consul管理器初始化失败: %v", err)
	}

	// 启动Consul管理器
	ctx := context.Background()
	if err := consulManager.Start(ctx); err != nil {
		log.Fatalf("Consul管理器启动失败: %v", err)
	}

	// 确保在程序退出时停止Consul管理器
	defer func() {
		if err := consulManager.Stop(); err != nil {
			log.Printf("停止Consul管理器失败: %v", err)
		}
	}()

	// 初始化健康检查处理器
	healthHandler := consul.NewHealthHandler(consulManager, "1.0.0")

	// 注册健康检查路由
	healthHandler.RegisterHealthRoutes(r)

	// 5. 启动网关服务
	server := &http.Server{
		Addr:    cfg.Server.Address,
		Handler: r,
	}

	log.Printf("用户与社区集群网关已启动，监听端口: %s", cfg.Server.Address)
	if err := server.ListenAndServe(); err != nil {
		log.Fatalf("网关服务启动失败: %v", err)
	}
}
