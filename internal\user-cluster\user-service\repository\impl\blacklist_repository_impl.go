package impl

import (
	"context"
	"pxpat-backend/internal/user-cluster/user-service/dto"
	"pxpat-backend/internal/user-cluster/user-service/model"
	"pxpat-backend/internal/user-cluster/user-service/repository"
	"pxpat-backend/pkg/errors"
	"time"

	"gorm.io/gorm"
)

// BlacklistRepositoryImpl 黑名单仓库实现
type BlacklistRepositoryImpl struct {
	db *gorm.DB
}

// NewBlacklistRepository 创建黑名单仓库实例
func NewBlacklistRepository(db *gorm.DB) repository.BlacklistRepository {
	return &BlacklistRepositoryImpl{
		db: db,
	}
}

// CreateBlacklist 创建黑名单关系
func (r *BlacklistRepositoryImpl) CreateBlacklist(ctx context.Context, blacklist *model.UserBlacklist) error {
	// 检查是否已存在黑名单关系
	var existingBlacklist model.UserBlacklist
	err := r.db.WithContext(ctx).Where("blocker_ksuid = ? AND blocked_ksuid = ?",
		blacklist.BlockerKSUID, blacklist.BlockedKSUID).First(&existingBlacklist).Error

	if err == nil {
		// 如果已存在且未删除，返回错误
		if existingBlacklist.DeletedAt == nil {
			return repository.ErrAlreadyBlacklisted
		}
		// 如果已删除，则恢复黑名单关系
		existingBlacklist.DeletedAt = nil
		existingBlacklist.UpdatedAt = time.Now()
		return r.db.WithContext(ctx).Save(&existingBlacklist).Error
	}

	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	// 创建新的黑名单关系
	return r.db.WithContext(ctx).Create(blacklist).Error
}

// DeleteBlacklist 删除黑名单关系（硬删除）
func (r *BlacklistRepositoryImpl) DeleteBlacklist(ctx context.Context, blockerKSUID, blockedKSUID string) error {
	result := r.db.WithContext(ctx).Where("blocker_ksuid = ? AND blocked_ksuid = ?",
		blockerKSUID, blockedKSUID).Delete(&model.UserBlacklist{})

	if result.Error != nil {
		return result.Error
	}

	if result.RowsAffected == 0 {
		return repository.ErrBlacklistNotFound
	}

	return nil
}

// GetBlacklist 获取黑名单关系
func (r *BlacklistRepositoryImpl) GetBlacklist(ctx context.Context, blockerKSUID, blockedKSUID string) (*model.UserBlacklist, error) {
	var blacklist model.UserBlacklist
	err := r.db.WithContext(ctx).Where("blocker_ksuid = ? AND blocked_ksuid = ?",
		blockerKSUID, blockedKSUID).First(&blacklist).Error

	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, repository.ErrBlacklistNotFound
	}

	return &blacklist, err
}

// IsBlacklisted 检查是否已拉黑
func (r *BlacklistRepositoryImpl) IsBlacklisted(ctx context.Context, blockerKSUID, blockedKSUID string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&model.UserBlacklist{}).
		Where("blocker_ksuid = ? AND blocked_ksuid = ?", blockerKSUID, blockedKSUID).Count(&count).Error

	return count > 0, err
}

// GetBlacklistUsers 获取用户的黑名单列表
func (r *BlacklistRepositoryImpl) GetBlacklistUsers(ctx context.Context, userKSUID string, page, pageSize int) ([]*dto.UserBlacklistInfo, int64, error) {
	var total int64
	var blacklists []model.UserBlacklist

	// 获取总数
	err := r.db.WithContext(ctx).Model(&model.UserBlacklist{}).
		Where("blocker_ksuid = ?", userKSUID).
		Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	offset := (page - 1) * pageSize
	err = r.db.WithContext(ctx).
		Preload("Blocked").
		Where("blocker_ksuid = ?", userKSUID).
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&blacklists).Error

	if err != nil {
		return nil, 0, err
	}

	// 转换为DTO
	var result []*dto.UserBlacklistInfo
	for _, blacklist := range blacklists {
		if blacklist.Blocked != nil {
			result = append(result, &dto.UserBlacklistInfo{
				UserKSUID:     blacklist.Blocked.UserKSUID,
				Username:      blacklist.Blocked.Username,
				Nickname:      blacklist.Blocked.Nickname,
				AvatarURL:     blacklist.Blocked.AvatarURL,
				UserType:      blacklist.Blocked.UserType,
				IsVerified:    blacklist.Blocked.IsVerified,
				BlacklistedAt: blacklist.CreatedAt,
			})
		}
	}

	return result, total, nil
}

// GetBlacklistCount 获取黑名单数量
func (r *BlacklistRepositoryImpl) GetBlacklistCount(ctx context.Context, userKSUID string) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&model.UserBlacklist{}).
		Where("blocker_ksuid = ?", userKSUID).
		Count(&count).Error
	return count, err
}

// BatchCheckBlacklistStatus 批量检查黑名单状态
func (r *BlacklistRepositoryImpl) BatchCheckBlacklistStatus(ctx context.Context, currentUserKSUID string, targetUserKSUIDs []string) ([]dto.UserBlacklistStatus, error) {
	var result []dto.UserBlacklistStatus

	for _, targetKSUID := range targetUserKSUIDs {
		// 检查是否拉黑对方
		isBlacklisted, err := r.IsBlacklisted(ctx, currentUserKSUID, targetKSUID)
		if err != nil {
			return nil, err
		}

		// 检查是否被对方拉黑
		isBlacklistedBy, err := r.IsBlacklisted(ctx, targetKSUID, currentUserKSUID)
		if err != nil {
			return nil, err
		}

		result = append(result, dto.UserBlacklistStatus{
			UserKSUID:       targetKSUID,
			IsBlacklisted:   isBlacklisted,
			IsBlacklistedBy: isBlacklistedBy,
		})
	}

	return result, nil
}

// UnblockFollow 取消屏蔽关注关系
func (r *BlacklistRepositoryImpl) UnblockFollow(ctx context.Context, followerKSUID, followeeKSUID string) error {
	result := r.db.WithContext(ctx).Model(&model.UserFollow{}).
		Where("follower_ksuid = ? AND followee_ksuid = ?", followerKSUID, followeeKSUID).
		Updates(map[string]interface{}{
			"status":     model.FollowStatusActive,
			"updated_at": time.Now(),
		})

	if result.Error != nil {
		return result.Error
	}

	if result.RowsAffected == 0 {
		return repository.ErrFollowNotFound
	}

	return nil
}

// BlockUserAndRemoveFollow 拉黑用户并删除关注关系
func (r *BlacklistRepositoryImpl) BlockUserAndRemoveFollow(ctx context.Context, blockerKSUID, blockedKSUID string) error {
	// 开启事务
	tx := r.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return tx.Error
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 删除被拉黑用户对拉黑者的关注关系（如果存在）
	err := tx.Where("follower_ksuid = ? AND followee_ksuid = ?", blockedKSUID, blockerKSUID).
		Delete(&model.UserFollow{}).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	// 2. 删除拉黑者对被拉黑用户的关注关系（如果存在）
	err = tx.Where("follower_ksuid = ? AND followee_ksuid = ?", blockerKSUID, blockedKSUID).
		Delete(&model.UserFollow{}).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	// 3. 创建黑名单关系
	blacklist := &model.UserBlacklist{
		BlockerKSUID: blockerKSUID,
		BlockedKSUID: blockedKSUID,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	// 检查是否已存在黑名单关系
	var existingBlacklist model.UserBlacklist
	err = tx.Where("blocker_ksuid = ? AND blocked_ksuid = ?", blockerKSUID, blockedKSUID).
		First(&existingBlacklist).Error

	if err == nil {
		// 如果已存在且未删除，更新时间
		if existingBlacklist.DeletedAt == nil {
			existingBlacklist.UpdatedAt = time.Now()
			err = tx.Save(&existingBlacklist).Error
		} else {
			// 如果已删除，则恢复黑名单关系
			existingBlacklist.DeletedAt = nil
			existingBlacklist.UpdatedAt = time.Now()
			err = tx.Save(&existingBlacklist).Error
		}
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		// 创建新的黑名单关系
		err = tx.Create(blacklist).Error
	}

	if err != nil {
		tx.Rollback()
		return err
	}

	// 4. 更新用户关注数量
	err = r.updateUserFollowCountsInTx(tx, blockerKSUID)
	if err != nil {
		tx.Rollback()
		return err
	}

	err = r.updateUserFollowCountsInTx(tx, blockedKSUID)
	if err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// updateUserFollowCountsInTx 在事务中更新用户关注数量
func (r *BlacklistRepositoryImpl) updateUserFollowCountsInTx(tx *gorm.DB, userKSUID string) error {
	// 获取关注数量
	var followingCount int64
	err := tx.Model(&model.UserFollow{}).
		Where("follower_ksuid = ? AND status = ?", userKSUID, model.FollowStatusActive).
		Count(&followingCount).Error
	if err != nil {
		return err
	}

	// 获取粉丝数量
	var followersCount int64
	err = tx.Model(&model.UserFollow{}).
		Where("followee_ksuid = ? AND status = ?", userKSUID, model.FollowStatusActive).
		Count(&followersCount).Error
	if err != nil {
		return err
	}

	// 更新用户表中的计数
	return tx.Model(&model.User{}).
		Where("user_ksuid = ?", userKSUID).
		Updates(map[string]interface{}{
			"following_count": followingCount,
			"followers_count": int(followersCount),
			"updated_at":      time.Now(),
		}).Error
}
