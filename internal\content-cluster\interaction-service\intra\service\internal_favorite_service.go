package service

import (
	"context"
	"fmt"
	"pxpat-backend/pkg/errors"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
	"pxpat-backend/internal/content-cluster/interaction-service/dto"
	"pxpat-backend/internal/content-cluster/interaction-service/model"
	"pxpat-backend/internal/content-cluster/interaction-service/repository"
)

// InternalFavoriteService 内部收藏服务实现
type InternalFavoriteService struct {
	favoriteFolderRepo repository.FavoriteFolderRepository
	favoriteItemRepo   repository.FavoriteItemRepository
	db                 *gorm.DB
}

// NewInternalFavoriteService 创建内部收藏服务实例
func NewInternalFavoriteService(
	favoriteFolderRepo repository.FavoriteFolderRepository,
	favoriteItemRepo repository.FavoriteItemRepository,
	db *gorm.DB,
) *InternalFavoriteService {
	return &InternalFavoriteService{
		favoriteFolderRepo: favoriteFolderRepo,
		favoriteItemRepo:   favoriteItemRepo,
		db:                 db,
	}
}

// AddToFavoriteInternal 内部添加到收藏夹
func (s *InternalFavoriteService) AddToFavoriteInternal(ctx context.Context, userKSUID string, contentKSUID string, contentType model.ContentType, folderID string) *errors.Errors {
	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", contentKSUID).
		Str("content_type", string(contentType)).
		Str("folder_id", folderID).
		Msg("内部添加到收藏夹")

	// 如果没有指定收藏夹，使用默认收藏夹
	if folderID == "" {
		defaultFolder, err := s.favoriteFolderRepo.GetDefaultFolder(ctx, userKSUID)
		if err != nil {
			// 创建默认收藏夹
			defaultFolder = model.NewDefaultFavoriteFolder(userKSUID)
			err = s.favoriteFolderRepo.Create(ctx, defaultFolder)
			if err != nil {
				log.Error().Err(err).
					Str("user_ksuid", userKSUID).
					Msg("为用户创建默认收藏夹失败")
				return errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.CREATE_RECORD_ERROR, err)
			}
		}
		folderID = defaultFolder.FavoriteFolderID
	}

	// 检查是否已经收藏
	exists, err := s.favoriteItemRepo.ExistsByUserAndContent(ctx, userKSUID, contentKSUID)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", contentKSUID).
			Str("folder_id", folderID).
			Msg("检查收藏项是否存在失败")
		return errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	if exists {
		log.Warn().
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", contentKSUID).
			Str("folder_id", folderID).
			Msg("内容已在收藏夹中")
		return nil // 已经收藏，不需要重复添加
	}

	// 添加到收藏夹
	//err = s.favoriteFolderRepo.(ctx, userKSUID, contentKSUID, contentType, folderID)
	//if err != nil {
	//	log.Error().Err(err).
	//		Str("user_ksuid", userKSUID).
	//		Str("content_ksuid", contentKSUID).
	//		Str("folder_id", folderID).
	//		Msg("添加到收藏夹失败")
	//	return errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.CREATE_RECORD_ERROR, err)
	//}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", contentKSUID).
		Str("folder_id", folderID).
		Msg("成功添加到收藏夹")

	return nil
}

// RemoveFromFavoriteInternal 内部从收藏夹移除
func (s *InternalFavoriteService) RemoveFromFavoriteInternal(ctx context.Context, userKSUID string, contentKSUID string, folderID string) *errors.Errors {
	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", contentKSUID).
		Str("folder_id", folderID).
		Msg("内部从收藏夹移除")

	// 如果没有指定收藏夹，从默认收藏夹移除
	if folderID == "" {
		defaultFolder, err := s.favoriteFolderRepo.GetDefaultFolder(ctx, userKSUID)
		if err != nil {
			log.Error().Err(err).
				Str("user_ksuid", userKSUID).
				Msg("获取默认收藏夹失败")
			return errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
		}
		folderID = defaultFolder.FavoriteFolderID
	}

	// 从收藏夹移除
	//err := s.favoriteRepo.RemoveFromFavorite(ctx, userKSUID, contentKSUID)
	//if err != nil {
	//	log.Error().Err(err).
	//		Str("user_ksuid", userKSUID).
	//		Str("content_ksuid", contentKSUID).
	//		Str("folder_id", folderID).
	//		Msg("从收藏夹移除失败")
	//	return errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.DELETE_RECORD_ERROR, err)
	//}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", contentKSUID).
		Str("folder_id", folderID).
		Msg("成功从收藏夹移除")

	return nil
}

// CheckFavoriteStatusInternal 内部检查收藏状态
func (s *InternalFavoriteService) CheckFavoriteStatusInternal(ctx context.Context, userKSUID string, contentKSUID string) (bool, []string, *errors.Errors) {
	log.Debug().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", contentKSUID).
		Msg("内部检查收藏状态")

	// 获取收藏项
	items, err := s.favoriteItemRepo.GetByUserAndContent(ctx, userKSUID, contentKSUID)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", contentKSUID).
			Msg("获取收藏项失败")
		return false, nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	if len(items) == 0 {
		return false, nil, nil
	}

	// 提取收藏夹ID列表
	folderIDs := make([]string, len(items))
	for i, item := range items {
		folderIDs[i] = item.FavoriteFolderID
	}

	return true, folderIDs, nil
}

// GetUserFavoriteStatsInternal 内部获取用户收藏统计
func (s *InternalFavoriteService) GetUserFavoriteStatsInternal(ctx context.Context, userKSUID string) (*dto.GetFavoriteStatsResponse, *errors.Errors) {
	log.Debug().
		Str("user_ksuid", userKSUID).
		Msg("内部获取用户收藏统计")

	// 获取收藏夹统计
	folderStats, err := s.favoriteFolderRepo.GetUserFolderStats(ctx, userKSUID)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Msg("获取收藏夹统计失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	// 注意：itemStats暂时不使用，因为folderStats已经包含了所需信息

	// 直接返回folderStats，因为它已经包含了正确的结构
	return folderStats, nil
}

// GetContentFavoriteCountInternal 内部获取内容收藏数
func (s *InternalFavoriteService) GetContentFavoriteCountInternal(ctx context.Context, contentKSUID string) (int64, *errors.Errors) {
	log.Debug().
		Str("content_ksuid", contentKSUID).
		Msg("内部获取内容收藏数")

	// 统计所有分表中该内容的收藏数
	var totalCount int64

	// 遍历所有分表统计
	for i := 0; i < 16; i++ {
		tableName := fmt.Sprintf("interaction_favorite_items_%d", i)

		// 检查表是否存在
		if !s.db.Migrator().HasTable(tableName) {
			continue
		}

		var count int64
		result := s.db.WithContext(ctx).
			Table(tableName).
			Where("content_ksuid = ?", contentKSUID).
			Count(&count)

		if result.Error != nil {
			log.Error().Err(result.Error).
				Str("content_ksuid", contentKSUID).
				Str("table_name", tableName).
				Msg("统计内容收藏数失败")
			return 0, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, result.Error)
		}

		totalCount += count
	}

	log.Debug().
		Str("content_ksuid", contentKSUID).
		Int64("total_count", totalCount).
		Msg("内部获取内容收藏数成功")

	return totalCount, nil
}

// BatchCheckFavoriteStatusInternal 内部批量检查收藏状态
func (s *InternalFavoriteService) BatchCheckFavoriteStatusInternal(ctx context.Context, userKSUID string, contentKSUIDs []string) (map[string]bool, *errors.Errors) {
	log.Debug().
		Str("user_ksuid", userKSUID).
		Int("content_count", len(contentKSUIDs)).
		Msg("内部批量检查收藏状态")

	if len(contentKSUIDs) == 0 {
		return make(map[string]bool), nil
	}

	// 批量检查收藏状态
	favoriteMap, err := s.favoriteItemRepo.BatchCheckFavoriteStatus(ctx, userKSUID, contentKSUIDs)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Msg("批量检查收藏状态失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	return favoriteMap, nil
}
