package test

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"pxpat-backend/internal/user-cluster/user-service/dto"
	"pxpat-backend/internal/user-cluster/user-service/intra/handler"
	"pxpat-backend/pkg/errors"
	globalTypes "pxpat-backend/pkg/types"
)

// MockBlacklistServiceForInternal 模拟黑名单服务（用于内部接口测试）
type MockBlacklistServiceForInternal struct {
	mock.Mock
}

func (m *MockBlacklistServiceForInternal) CheckBlacklistStatus(ctx context.Context, currentUserKSUID, targetUserKSUID string) (*dto.CheckBlacklistStatusResponse, *errors.Errors) {
	args := m.Called(ctx, currentUserKSUID, targetUserKSUID)
	if args.Get(0) == nil {
		return nil, args.Get(1).(*errors.Errors)
	}
	return args.Get(0).(*dto.CheckBlacklistStatusResponse), nil
}

func (m *MockBlacklistServiceForInternal) BlockUser(ctx context.Context, blockerKSUID, blockedKSUID string) (*dto.BlockUserResponse, *errors.Errors) {
	args := m.Called(ctx, blockerKSUID, blockedKSUID)
	if args.Get(0) == nil {
		return nil, args.Get(1).(*errors.Errors)
	}
	return args.Get(0).(*dto.BlockUserResponse), nil
}

func (m *MockBlacklistServiceForInternal) UnblockUser(ctx context.Context, blockerKSUID, blockedKSUID string) (*dto.BlockUserResponse, *errors.Errors) {
	args := m.Called(ctx, blockerKSUID, blockedKSUID)
	if args.Get(0) == nil {
		return nil, args.Get(1).(*errors.Errors)
	}
	return args.Get(0).(*dto.BlockUserResponse), nil
}

func (m *MockBlacklistServiceForInternal) GetBlacklist(ctx context.Context, userKSUID string, page, pageSize int) (*dto.BlacklistResponse, *errors.Errors) {
	args := m.Called(ctx, userKSUID, page, pageSize)
	if args.Get(0) == nil {
		return nil, args.Get(1).(*errors.Errors)
	}
	return args.Get(0).(*dto.BlacklistResponse), nil
}

// TestInternalBlacklistHandler_CheckBlacklistStatusForService 测试内部检查黑名单状态接口
func TestInternalBlacklistHandler_CheckBlacklistStatusForService(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("成功检查黑名单状态", func(t *testing.T) {
		// 创建模拟服务
		mockService := &MockBlacklistServiceForInternal{}

		// 创建处理器，使用模拟服务的接口
		handler := handler.NewInternalBlacklistHandler(mockService)

		// 设置期望的服务调用
		expectedResponse := &dto.CheckBlacklistStatusResponse{
			IsBlacklisted:   true,
			IsBlacklistedBy: false,
		}
		mockService.On("CheckBlacklistStatus", mock.Anything, "user1", "user2").Return(expectedResponse, (*errors.Errors)(nil))

		// 创建请求
		reqBody := dto.InternalCheckBlacklistStatusRequest{
			CurrentUserKSUID: "user1",
			TargetUserKSUID:  "user2",
		}
		jsonBody, _ := json.Marshal(reqBody)

		// 创建HTTP请求
		req, _ := http.NewRequest("POST", "/intra/blacklist/check-status", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		// 创建响应记录器
		w := httptest.NewRecorder()

		// 创建Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用处理器
		handler.CheckBlacklistStatusForService(c)

		// 验证响应状态码
		assert.Equal(t, http.StatusOK, w.Code)

		// 解析响应
		var response globalTypes.GlobalResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// 验证响应数据结构
		assert.NotNil(t, response.Data)
	})

	t.Run("请求参数无效", func(t *testing.T) {
		// 创建模拟服务
		mockService := &MockBlacklistServiceForInternal{}

		// 创建处理器
		handler := handler.NewInternalBlacklistHandler(mockService)

		// 创建无效请求（缺少必需字段）
		reqBody := map[string]interface{}{
			"current_user_ksuid": "", // 空值，应该失败
		}
		jsonBody, _ := json.Marshal(reqBody)

		// 创建HTTP请求
		req, _ := http.NewRequest("POST", "/intra/blacklist/check-status", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		// 创建响应记录器
		w := httptest.NewRecorder()

		// 创建Gin上下文
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// 调用处理器
		handler.CheckBlacklistStatusForService(c)

		// 验证响应状态码
		assert.Equal(t, http.StatusBadRequest, w.Code)

		// 解析响应
		var response globalTypes.GlobalResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// 验证错误响应
		assert.NotEqual(t, "SUCCESS", response.Code)
	})
}
