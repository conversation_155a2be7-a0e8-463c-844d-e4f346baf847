package impl

import (
	"context"
	"pxpat-backend/internal/user-cluster/user-service/dto"
	"pxpat-backend/internal/user-cluster/user-service/model"
	"pxpat-backend/internal/user-cluster/user-service/repository"
	"pxpat-backend/pkg/errors"
	"time"

	"gorm.io/gorm"
)

// FollowRepositoryImpl 关注仓库实现
type FollowRepositoryImpl struct {
	db *gorm.DB
}

// NewFollowRepository 创建关注仓库实例
func NewFollowRepository(db *gorm.DB) repository.FollowRepository {
	return &FollowRepositoryImpl{
		db: db,
	}
}

// CreateFollow 创建关注关系
func (r *FollowRepositoryImpl) CreateFollow(ctx context.Context, follow *model.UserFollow) error {
	// 检查是否被对方拉黑
	var blacklistCount int64
	err := r.db.WithContext(ctx).Model(&model.UserBlacklist{}).
		Where("blocker_ksuid = ? AND blocked_ksuid = ?", follow.FolloweeKSUID, follow.FollowerKSUID).
		Count(&blacklistCount).Error
	if err != nil {
		return err
	}
	if blacklistCount > 0 {
		return repository.ErrCannotFollowSelf // 重用这个错误，或者定义新的错误
	}

	// 检查是否已存在关注关系
	var existingFollow model.UserFollow
	err = r.db.WithContext(ctx).Where("follower_ksuid = ? AND followee_ksuid = ?",
		follow.FollowerKSUID, follow.FolloweeKSUID).First(&existingFollow).Error

	if err == nil {
		// 如果已存在且未删除，返回错误
		if existingFollow.DeletedAt == nil {
			return repository.ErrAlreadyFollowing
		}
		// 如果已删除，则恢复关注关系
		existingFollow.DeletedAt = nil
		existingFollow.Status = model.FollowStatusActive
		existingFollow.UpdatedAt = time.Now()
		return r.db.WithContext(ctx).Save(&existingFollow).Error
	}

	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	// 创建新的关注关系
	return r.db.WithContext(ctx).Create(follow).Error
}

// DeleteFollow 删除关注关系（硬删除）
func (r *FollowRepositoryImpl) DeleteFollow(ctx context.Context, followerKSUID, followeeKSUID string) error {
	result := r.db.WithContext(ctx).Where("follower_ksuid = ? AND followee_ksuid = ?",
		followerKSUID, followeeKSUID).Delete(&model.UserFollow{})

	if result.Error != nil {
		return result.Error
	}

	if result.RowsAffected == 0 {
		return repository.ErrFollowNotFound
	}

	return nil
}

// GetFollow 获取关注关系
func (r *FollowRepositoryImpl) GetFollow(ctx context.Context, followerKSUID, followeeKSUID string) (*model.UserFollow, error) {
	var follow model.UserFollow
	err := r.db.WithContext(ctx).Where("follower_ksuid = ? AND followee_ksuid = ?",
		followerKSUID, followeeKSUID).First(&follow).Error

	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, repository.ErrFollowNotFound
	}

	return &follow, err
}

// IsFollowing 检查是否已关注
func (r *FollowRepositoryImpl) IsFollowing(ctx context.Context, followerKSUID, followeeKSUID string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&model.UserFollow{}).
		Where("follower_ksuid = ? AND followee_ksuid = ? AND status = ?",
			followerKSUID, followeeKSUID, model.FollowStatusActive).Count(&count).Error

	return count > 0, err
}

// GetFollowers 获取粉丝列表
func (r *FollowRepositoryImpl) GetFollowers(ctx context.Context, userKSUID string, page, pageSize int) ([]*dto.UserFollowInfo, int64, error) {
	var total int64
	var follows []model.UserFollow

	// 获取总数
	err := r.db.WithContext(ctx).Model(&model.UserFollow{}).
		Where("followee_ksuid = ? AND status = ?", userKSUID, model.FollowStatusActive).
		Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	offset := (page - 1) * pageSize
	err = r.db.WithContext(ctx).
		Preload("Follower").
		Where("followee_ksuid = ? AND status = ?", userKSUID, model.FollowStatusActive).
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&follows).Error

	if err != nil {
		return nil, 0, err
	}

	// 转换为DTO
	var result []*dto.UserFollowInfo
	for _, follow := range follows {
		if follow.Follower != nil {
			result = append(result, &dto.UserFollowInfo{
				UserKSUID:      follow.Follower.UserKSUID,
				Username:       follow.Follower.Username,
				Nickname:       follow.Follower.Nickname,
				AvatarURL:      follow.Follower.AvatarURL,
				UserType:       follow.Follower.UserType,
				IsVerified:     follow.Follower.IsVerified,
				FollowedAt:     follow.CreatedAt,
				FollowingCount: follow.Follower.FollowingCount,
				FollowersCount: int(follow.Follower.FollowersCount),
			})
		}
	}

	return result, total, nil
}

// GetFollowing 获取关注列表
func (r *FollowRepositoryImpl) GetFollowing(ctx context.Context, userKSUID string, page, pageSize int) ([]*dto.UserFollowInfo, int64, error) {
	var total int64
	var follows []model.UserFollow

	// 获取总数
	err := r.db.WithContext(ctx).Model(&model.UserFollow{}).
		Where("follower_ksuid = ? AND status = ?", userKSUID, model.FollowStatusActive).
		Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	offset := (page - 1) * pageSize
	err = r.db.WithContext(ctx).
		Preload("Followee").
		Where("follower_ksuid = ? AND status = ?", userKSUID, model.FollowStatusActive).
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&follows).Error

	if err != nil {
		return nil, 0, err
	}

	// 转换为DTO
	var result []*dto.UserFollowInfo
	for _, follow := range follows {
		if follow.Followee != nil {
			result = append(result, &dto.UserFollowInfo{
				UserKSUID:      follow.Followee.UserKSUID,
				Username:       follow.Followee.Username,
				Nickname:       follow.Followee.Nickname,
				AvatarURL:      follow.Followee.AvatarURL,
				UserType:       follow.Followee.UserType,
				IsVerified:     follow.Followee.IsVerified,
				FollowedAt:     follow.CreatedAt,
				FollowingCount: follow.Followee.FollowingCount,
				FollowersCount: int(follow.Followee.FollowersCount),
			})
		}
	}

	return result, total, nil
}

// GetFollowersCount 获取粉丝数量
func (r *FollowRepositoryImpl) GetFollowersCount(ctx context.Context, userKSUID string) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&model.UserFollow{}).
		Where("followee_ksuid = ? AND status = ?", userKSUID, model.FollowStatusActive).
		Count(&count).Error
	return count, err
}

// GetFollowingCount 获取关注数量
func (r *FollowRepositoryImpl) GetFollowingCount(ctx context.Context, userKSUID string) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&model.UserFollow{}).
		Where("follower_ksuid = ? AND status = ?", userKSUID, model.FollowStatusActive).
		Count(&count).Error
	return count, err
}

// UpdateUserFollowCounts 更新用户的关注和粉丝数量
func (r *FollowRepositoryImpl) UpdateUserFollowCounts(ctx context.Context, userKSUID string) error {
	// 获取关注数量
	followingCount, err := r.GetFollowingCount(ctx, userKSUID)
	if err != nil {
		return err
	}

	// 获取粉丝数量
	followersCount, err := r.GetFollowersCount(ctx, userKSUID)
	if err != nil {
		return err
	}

	// 更新用户表中的计数
	return r.db.WithContext(ctx).Model(&model.User{}).
		Where("user_ksuid = ?", userKSUID).
		Updates(map[string]interface{}{
			"following_count": followingCount,
			"followers_count": int(followersCount),
			"updated_at":      time.Now(),
		}).Error
}

// BatchCheckFollowStatus 批量检查关注状态
func (r *FollowRepositoryImpl) BatchCheckFollowStatus(ctx context.Context, currentUserKSUID string, targetUserKSUIDs []string) ([]dto.UserFollowStatus, error) {
	var result []dto.UserFollowStatus

	for _, targetKSUID := range targetUserKSUIDs {
		// 检查是否关注对方
		isFollowing, err := r.IsFollowing(ctx, currentUserKSUID, targetKSUID)
		if err != nil {
			return nil, err
		}

		// 检查是否被对方关注
		isFollowedBy, err := r.IsFollowing(ctx, targetKSUID, currentUserKSUID)
		if err != nil {
			return nil, err
		}

		result = append(result, dto.UserFollowStatus{
			UserKSUID:      targetKSUID,
			IsFollowing:    isFollowing,
			IsFollowedBy:   isFollowedBy,
			IsMutualFollow: isFollowing && isFollowedBy,
		})
	}

	return result, nil
}

// GetMutualFollows 获取互相关注的用户列表
func (r *FollowRepositoryImpl) GetMutualFollows(ctx context.Context, userKSUID string, page, pageSize int) ([]*dto.UserFollowInfo, int64, error) {
	var total int64
	var follows []model.UserFollow

	// 子查询：获取当前用户关注的人中，也关注了当前用户的人
	subQuery := r.db.WithContext(ctx).Model(&model.UserFollow{}).
		Select("follower_ksuid").
		Where("followee_ksuid = ? AND status = ?", userKSUID, model.FollowStatusActive)

	// 获取总数
	err := r.db.WithContext(ctx).Model(&model.UserFollow{}).
		Where("follower_ksuid = ? AND status = ? AND followee_ksuid IN (?)",
			userKSUID, model.FollowStatusActive, subQuery).
		Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	offset := (page - 1) * pageSize
	err = r.db.WithContext(ctx).
		Preload("Followee").
		Where("follower_ksuid = ? AND status = ? AND followee_ksuid IN (?)",
			userKSUID, model.FollowStatusActive, subQuery).
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&follows).Error

	if err != nil {
		return nil, 0, err
	}

	// 转换为DTO
	var result []*dto.UserFollowInfo
	for _, follow := range follows {
		if follow.Followee != nil {
			result = append(result, &dto.UserFollowInfo{
				UserKSUID:      follow.Followee.UserKSUID,
				Username:       follow.Followee.Username,
				Nickname:       follow.Followee.Nickname,
				AvatarURL:      follow.Followee.AvatarURL,
				UserType:       follow.Followee.UserType,
				IsVerified:     follow.Followee.IsVerified,
				FollowedAt:     follow.CreatedAt,
				FollowingCount: follow.Followee.FollowingCount,
				FollowersCount: int(follow.Followee.FollowersCount),
			})
		}
	}

	return result, total, nil
}



// GetFollowStats 获取关注/被关注统计信息
func (r *FollowRepositoryImpl) GetFollowStats(ctx context.Context, userKSUID string) (*dto.FollowStatsResponse, error) {
	followingCount, err := r.GetFollowingCount(ctx, userKSUID)
	if err != nil {
		return nil, err
	}

	followersCount, err := r.GetFollowersCount(ctx, userKSUID)
	if err != nil {
		return nil, err
	}

	return &dto.FollowStatsResponse{
		FollowingCount: followingCount,
		FollowersCount: followersCount,
	}, nil
}



// updateUserFollowCountsInTx 在事务中更新用户关注数量
func (r *FollowRepositoryImpl) updateUserFollowCountsInTx(tx *gorm.DB, userKSUID string) error {
	// 获取关注数量
	var followingCount int64
	err := tx.Model(&model.UserFollow{}).
		Where("follower_ksuid = ? AND status = ?", userKSUID, model.FollowStatusActive).
		Count(&followingCount).Error
	if err != nil {
		return err
	}

	// 获取粉丝数量
	var followersCount int64
	err = tx.Model(&model.UserFollow{}).
		Where("followee_ksuid = ? AND status = ?", userKSUID, model.FollowStatusActive).
		Count(&followersCount).Error
	if err != nil {
		return err
	}

	// 更新用户表中的计数
	return tx.Model(&model.User{}).
		Where("user_ksuid = ?", userKSUID).
		Updates(map[string]interface{}{
			"following_count": followingCount,
			"followers_count": int(followersCount),
			"updated_at":      time.Now(),
		}).Error
}
