# 关注功能 API 文档

## 概述

用户关注功能提供了完整的关注/取消关注、粉丝列表、关注列表等功能。

## 接口列表

### 1. 关注用户

**接口地址：** `POST /api/follow/user`

**请求头：**
```
Authorization: Bearer <token>
Content-Type: application/json
```

**请求参数：**
```json
{
  "followee_ksuid": "用户KSUID"
}
```

**响应示例：**
```json
{
  "code": "SUCCESS",
  "message": "操作成功",
  "data": {
    "success": true,
    "message": "成功关注 用户昵称",
    "followee_ksuid": "用户KSUID",
    "is_following": true
  }
}
```

### 2. 取消关注用户

**接口地址：** `DELETE /api/follow/user`

**请求头：**
```
Authorization: Bearer <token>
Content-Type: application/json
```

**请求参数：**
```json
{
  "followee_ksuid": "用户KSUID"
}
```

**响应示例：**
```json
{
  "code": "SUCCESS",
  "message": "操作成功",
  "data": {
    "success": true,
    "message": "取消关注成功",
    "followee_ksuid": "用户KSUID",
    "is_following": false
  }
}
```

### 3. 检查关注状态

**接口地址：** `GET /api/follow/status?followee_ksuid=用户KSUID`

**请求头：**
```
Authorization: Bearer <token>
```

**响应示例：**
```json
{
  "code": "SUCCESS",
  "message": "获取成功",
  "data": {
    "is_following": true,
    "is_followed_by": false,
    "is_mutual_follow": false
  }
}
```

### 4. 批量检查关注状态

**接口地址：** `POST /api/follow/status/batch`

**请求头：**
```
Authorization: Bearer <token>
Content-Type: application/json
```

**请求参数：**
```json
{
  "user_ksuids": ["用户KSUID1", "用户KSUID2"]
}
```

**响应示例：**
```json
{
  "code": "SUCCESS",
  "message": "获取成功",
  "data": {
    "follow_statuses": [
      {
        "user_ksuid": "用户KSUID1",
        "is_following": true,
        "is_followed_by": false,
        "is_mutual_follow": false
      },
      {
        "user_ksuid": "用户KSUID2",
        "is_following": false,
        "is_followed_by": true,
        "is_mutual_follow": false
      }
    ]
  }
}
```

### 5. 获取关注统计信息

**接口地址：** `GET /api/follow/stats?user_ksuid=用户KSUID`

**请求头：**
```
Authorization: Bearer <token>
```

**响应示例：**
```json
{
  "code": "SUCCESS",
  "message": "获取成功",
  "data": {
    "following_count": 123,
    "followers_count": 456
  }
}
```

### 6. 获取粉丝列表

**接口地址：** `GET /api/users/followers?user_ksuid=用户KSUID&page=1&page_size=20`

**请求头：**
```
Authorization: Bearer <token> (可选，如果要获取自己的粉丝列表)
```

**响应示例：**
```json
{
  "code": "SUCCESS",
  "message": "获取成功",
  "data": {
    "users": [
      {
        "user_ksuid": "用户KSUID",
        "username": "用户名",
        "nickname": "昵称",
        "avatar_url": "头像URL",
        "user_type": "用户类型",
        "is_verified": true,
        "followed_at": "2023-01-01T00:00:00Z",
        "following_count": 100,
        "fan_count": 200
      }
    ],
    "total": 456,
    "page": 1,
    "page_size": 20,
    "total_pages": 23
  }
}
```

### 7. 获取关注列表

**接口地址：** `GET /api/users/following?user_ksuid=用户KSUID&page=1&page_size=20`

**请求头：**
```
Authorization: Bearer <token> (可选，如果要获取自己的关注列表)
```

**响应示例：**
```json
{
  "code": "SUCCESS",
  "message": "获取成功",
  "data": {
    "users": [
      {
        "user_ksuid": "用户KSUID",
        "username": "用户名",
        "nickname": "昵称",
        "avatar_url": "头像URL",
        "user_type": "用户类型",
        "is_verified": true,
        "followed_at": "2023-01-01T00:00:00Z",
        "following_count": 100,
        "fan_count": 200
      }
    ],
    "total": 123,
    "page": 1,
    "page_size": 20,
    "total_pages": 7
  }
}
```

### 8. 获取自己的粉丝列表

**接口地址：** `GET /api/users/me/followers?page=1&page_size=20`

**请求头：**
```
Authorization: Bearer <token>
```

### 9. 获取自己的关注列表

**接口地址：** `GET /api/users/me/following?page=1&page_size=20`

**请求头：**
```
Authorization: Bearer <token>
```

## 数据库表结构

### user_follows 表

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigserial | 自增主键 |
| follower_ksuid | char(27) | 关注者用户KSUID |
| followee_ksuid | char(27) | 被关注者用户KSUID |
| status | varchar(16) | 关注状态 (active/blocked) |
| created_at | timestamp | 关注时间 |
| updated_at | timestamp | 更新时间 |
| deleted_at | timestamp | 软删除时间 |

### 索引

- `idx_follower_ksuid`: follower_ksuid
- `idx_followee_ksuid`: followee_ksuid
- `idx_follower_followee`: (follower_ksuid, followee_ksuid) 唯一索引
- `idx_deleted_at`: deleted_at

## 错误码

| 错误码 | 说明 |
|--------|------|
| UNAUTHORIZED | 用户未登录 |
| INVALID_REQUEST | 请求参数格式错误 |
| INTERNAL_ERROR | 内部服务器错误 |

## 注意事项

1. 所有需要认证的接口都需要在请求头中携带有效的JWT token
2. 用户不能关注自己
3. 重复关注会返回相应的提示信息
4. 粉丝和关注列表支持分页，默认每页20条，最大100条
5. 批量检查关注状态最多支持100个用户
6. 关注关系支持软删除，取消关注后可以重新关注
