package service

import (
	"context"
	"fmt"
	"math"
	"pxpat-backend/pkg/errors"

	"github.com/rs/zerolog/log"
	"pxpat-backend/internal/content-cluster/interaction-service/client"
	"pxpat-backend/internal/content-cluster/interaction-service/dto"
	"pxpat-backend/internal/content-cluster/interaction-service/model"
	"pxpat-backend/internal/content-cluster/interaction-service/repository"
	repositoryErrors "pxpat-backend/pkg/errors/repository"
)

// FavoriteItemService 收藏项服务实现
type FavoriteItemService struct {
	favoriteFolderRepo repository.FavoriteFolderRepository
	favoriteItemRepo   repository.FavoriteItemRepository
	favoriteStatsRepo  repository.FavoriteStatsRepository
	likeRepo           repository.LikeRepository
	userClient         client.UserServiceClient
	videoClient        client.VideoServiceClient
	novelClient        client.NovelServiceClient
	musicClient        client.MusicServiceClient
}

// NewFavoriteItemService 创建收藏项服务实例
func NewFavoriteItemService(
	favoriteFolderRepo repository.FavoriteFolderRepository,
	favoriteItemRepo repository.FavoriteItemRepository,
	favoriteStatsRepo repository.FavoriteStatsRepository,
	likeRepo repository.LikeRepository,
	userClient client.UserServiceClient,
	videoClient client.VideoServiceClient,
	novelClient client.NovelServiceClient,
	musicClient client.MusicServiceClient,
) *FavoriteItemService {
	return &FavoriteItemService{
		favoriteFolderRepo: favoriteFolderRepo,
		favoriteItemRepo:   favoriteItemRepo,
		favoriteStatsRepo:  favoriteStatsRepo,
		likeRepo:           likeRepo,
		userClient:         userClient,
		videoClient:        videoClient,
		novelClient:        novelClient,
		musicClient:        musicClient,
	}
}

// getContentInfoByType 根据内容类型从相应的服务获取内容信息
func (s *FavoriteItemService) getContentInfoByType(contentKSUID, contentType string) (*client.ContentInfo, *errors.Errors) {
	switch contentType {
	case "video", "anime", "short":
		// 视频、动漫、短视频都通过video服务处理
		content, err := s.videoClient.GetContentInfo(contentKSUID)
		if err != nil {
			return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
		}
		return content, nil
	case "novel":
		// 小说通过novel服务处理
		content, err := s.novelClient.GetContentInfo(contentKSUID)
		if err != nil {
			return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
		}
		return content, nil
	case "music":
		// 音乐通过music服务处理
		content, err := s.musicClient.GetContentInfo(contentKSUID)
		if err != nil {
			return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
		}
		return content, nil
	default:
		return nil, errors.NewGlobalErrors(errors.INVALID_PARAMETER, errors.INVALID_PARAMETER, fmt.Errorf("不支持的内容类型: %s", contentType))
	}
}







// ManageFavorite 管理收藏（合并添加和删除功能）
func (s *FavoriteItemService) ManageFavorite(ctx context.Context, userKSUID string, req *dto.ManageFavoriteRequest) (*dto.BatchFavoriteOperationResponse, *errors.Errors) {
	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Str("content_type", req.ContentType).
		Strs("add_folder_ids", req.AddFavoriteFolderIDs).
		Strs("del_folder_ids", req.DelFavoriteFolderIDs).
		Msg("正在管理收藏")

	var totalRequested, successCount, skippedCount int
	var isFirstTimeFavorite bool

	// 根据内容类型验证内容是否存在
	content, gErr := s.getContentInfoByType(req.ContentKSUID, req.ContentType)
	if gErr != nil {
		log.Error().Err(gErr).
			Str("content_ksuid", req.ContentKSUID).
			Str("content_type", req.ContentType).
			Msg("内容不存在")
		return nil, gErr
	}

	// 验证内容类型
	contentType := model.ContentType(req.ContentType)
	if !model.IsValidContentType(contentType) {
		if content != nil && content.ContentType != "" {
			contentType = model.ContentType(content.ContentType) // 使用内容服务返回的类型
		} else {
			return nil, errors.NewGlobalErrors(errors.INVALID_PARAMETER, errors.INVALID_PARAMETER, repositoryErrors.ErrInvalidContentType)
		}
	}

	// 检查用户在操作前是否收藏了这个内容
	existingItems, checkErr := s.favoriteItemRepo.GetByUserAndContentKSUID(ctx, userKSUID, req.ContentKSUID)
	wasAlreadyFavorited := checkErr == nil && len(existingItems) > 0

	// 处理删除操作
	if len(req.DelFavoriteFolderIDs) > 0 {
		totalRequested += len(req.DelFavoriteFolderIDs)

		// 获取实际需要删除的收藏夹ID（只删除存在的）
		var actualDelFolderIDs []string
		if checkErr == nil {
			existingFolderMap := make(map[string]bool)
			for _, item := range existingItems {
				existingFolderMap[item.FavoriteFolderID] = true
			}

			for _, folderID := range req.DelFavoriteFolderIDs {
				if existingFolderMap[folderID] {
					actualDelFolderIDs = append(actualDelFolderIDs, folderID)
				} else {
					skippedCount++ // 不存在的收藏项，跳过
				}
			}
		} else {
			// 如果检查失败，说明没有收藏项，全部跳过
			skippedCount += len(req.DelFavoriteFolderIDs)
		}

		// 执行删除操作
		if len(actualDelFolderIDs) > 0 {
			err := s.favoriteItemRepo.DeleteByContentAndFolders(ctx, userKSUID, req.ContentKSUID, actualDelFolderIDs)
			if err != nil {
				log.Error().Err(err).
					Str("user_ksuid", userKSUID).
					Str("content_ksuid", req.ContentKSUID).
					Strs("folder_ids", actualDelFolderIDs).
					Msg("从收藏夹删除内容失败")
				return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.DELETE_RECORD_ERROR, err)
			}

			successCount += len(actualDelFolderIDs)

			// 更新收藏夹的收藏项数量
			for _, folderID := range actualDelFolderIDs {
				err := s.favoriteFolderRepo.UpdateItemCount(ctx, folderID, false)
				if err != nil {
					log.Error().Err(err).
						Str("user_ksuid", userKSUID).
						Str("folder_id", folderID).
						Msg("更新收藏夹收藏项数量失败")
					// 不返回错误，因为删除操作已经成功
				}
			}

			log.Info().
				Str("user_ksuid", userKSUID).
				Str("content_ksuid", req.ContentKSUID).
				Strs("deleted_folder_ids", actualDelFolderIDs).
				Msg("从收藏夹删除内容成功")
		}
	}

	// 处理添加操作
	if len(req.AddFavoriteFolderIDs) > 0 {
		totalRequested += len(req.AddFavoriteFolderIDs)

		// 如果没有指定收藏夹，添加到默认收藏夹
		folderIDs := req.AddFavoriteFolderIDs
		if len(folderIDs) == 0 {
			folderIDs = []string{""} // 空字符串表示使用默认收藏夹
		}

		// 检查重复收藏（只添加不存在的）
		var actualAddFolderIDs []string
		if checkErr == nil {
			existingFolderMap := make(map[string]bool)
			for _, item := range existingItems {
				existingFolderMap[item.FavoriteFolderID] = true
			}

			for _, folderID := range folderIDs {
				if !existingFolderMap[folderID] {
					actualAddFolderIDs = append(actualAddFolderIDs, folderID)
				} else {
					skippedCount++ // 已存在的收藏项，跳过
				}
			}
		} else {
			// 如果检查失败，说明没有收藏项，全部添加
			actualAddFolderIDs = folderIDs
		}

		// 检查是否是第一次收藏这个内容
		if !wasAlreadyFavorited && len(actualAddFolderIDs) > 0 {
			isFirstTimeFavorite = true
		}

		// 执行添加操作
		if len(actualAddFolderIDs) > 0 {
			// 处理默认收藏夹
			for i, folderID := range actualAddFolderIDs {
				if folderID == "" {
					defaultFolder, err := s.favoriteFolderRepo.GetDefaultFolder(ctx, userKSUID)
					if err != nil {
						log.Error().Err(err).
							Str("user_ksuid", userKSUID).
							Msg("获取默认收藏夹失败")
						return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
					}
					actualAddFolderIDs[i] = defaultFolder.FavoriteFolderID
				}
			}

			// 批量添加到收藏夹
			var items []*model.FavoriteItem
			for _, folderID := range actualAddFolderIDs {
				item := model.NewFavoriteItem(userKSUID, req.ContentKSUID, contentType, folderID)
				items = append(items, item)
			}

			err := s.favoriteItemRepo.BatchCreate(ctx, items)
			if err != nil {
				log.Error().Err(err).
					Str("user_ksuid", userKSUID).
					Str("content_ksuid", req.ContentKSUID).
					Strs("folder_ids", actualAddFolderIDs).
					Msg("批量添加到收藏夹失败")
				return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.CREATE_RECORD_ERROR, err)
			}

			successCount += len(actualAddFolderIDs)

			// 更新收藏夹的收藏项数量
			for _, folderID := range actualAddFolderIDs {
				err := s.favoriteFolderRepo.UpdateItemCount(ctx, folderID, true)
				if err != nil {
					log.Error().Err(err).
						Str("user_ksuid", userKSUID).
						Str("folder_id", folderID).
						Msg("更新收藏夹收藏项数量失败")
					// 不返回错误，因为添加操作已经成功
				}
			}

			log.Info().
				Str("user_ksuid", userKSUID).
				Str("content_ksuid", req.ContentKSUID).
				Strs("added_folder_ids", actualAddFolderIDs).
				Msg("批量添加到收藏夹成功")
		}
	}

	// 检查用户在操作后是否还有收藏这个内容
	finalItems, finalCheckErr := s.favoriteItemRepo.GetByUserAndContentKSUID(ctx, userKSUID, req.ContentKSUID)
	isStillFavorited := finalCheckErr == nil && len(finalItems) > 0

	// 更新收藏统计
	if isFirstTimeFavorite {
		// 第一次收藏，增加统计
		err := s.favoriteStatsRepo.IncrementFavoriteCount(ctx, req.ContentKSUID, contentType)
		if err != nil {
			log.Error().Err(err).
				Str("user_ksuid", userKSUID).
				Str("content_ksuid", req.ContentKSUID).
				Msg("增加收藏统计失败")
		}
	} else if wasAlreadyFavorited && !isStillFavorited {
		// 之前收藏了，现在完全取消收藏，减少统计
		err := s.favoriteStatsRepo.DecrementFavoriteCount(ctx, req.ContentKSUID)
		if err != nil {
			log.Error().Err(err).
				Str("user_ksuid", userKSUID).
				Str("content_ksuid", req.ContentKSUID).
				Msg("减少收藏统计失败")
		}
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Int("total_requested", totalRequested).
		Int("success_count", successCount).
		Int("skipped_count", skippedCount).
		Bool("is_first_time_favorite", isFirstTimeFavorite).
		Bool("was_already_favorited", wasAlreadyFavorited).
		Bool("is_still_favorited", isStillFavorited).
		Msg("管理收藏完成")

	return &dto.BatchFavoriteOperationResponse{
		Success:        true,
		TotalRequested: totalRequested,
		SuccessCount:   successCount,
		SkippedCount:   skippedCount,
	}, nil
}

// GetFavoriteItems 获取收藏项列表
func (s *FavoriteItemService) GetFavoriteItems(ctx context.Context, currentUserKSUID string, req *dto.GetFavoriteItemsRequest) (*dto.GetFavoriteItemsResponse, *errors.Errors) {
	log.Info().
		Str("current_user_ksuid", currentUserKSUID).
		Str("folder_id", req.FavoriteFolderID).
		Str("content_type", req.ContentType).
		Int("page", req.Page).
		Int("page_size", req.PageSize).
		Msg("正在获取收藏项列表")

	var items []*model.FavoriteItem
	var total int64
	var err error

	folderInfo, err := s.favoriteFolderRepo.GetByFolderKSUID(ctx, req.FavoriteFolderID)
	if err != nil {
		log.Error().Err(err).
			Str("current_user_ksuid", currentUserKSUID).
			Str("folder_id", req.FavoriteFolderID).
			Str("content_type", req.ContentType).
			Int("page", req.Page).
			Int("page_size", req.PageSize).
			Msg("获取收藏项列表失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	if !folderInfo.IsPublic && folderInfo.UserKSUID != currentUserKSUID {
		log.Error().
			Str("current_user_ksuid", currentUserKSUID).
			Str("folder_id", req.FavoriteFolderID).
			Str("content_type", req.ContentType).
			Msg("BYD,想看别人私有收藏夹")
		return nil, errors.NewGlobalErrors(errors.PERMISSION_DENIED, errors.PERMISSION_DENIED, fmt.Errorf("permission denied"))
	}

	// 根据查询条件获取收藏项
	if req.ContentType != "" {
		// 按收藏夹和内容类型查询
		items, total, err = s.favoriteItemRepo.GetByFolderAndContentType(ctx, folderInfo.UserKSUID, req.FavoriteFolderID, model.ContentType(req.ContentType), req.Page, req.PageSize)
	} else {
		// 按收藏夹查询
		items, total, err = s.favoriteItemRepo.GetByFolderKSUID(ctx, folderInfo.UserKSUID, req.FavoriteFolderID, req.Page, req.PageSize)
	}

	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", folderInfo.UserKSUID).
			Str("folder_id", req.FavoriteFolderID).
			Msg("获取收藏项列表失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	// 批量获取内容信息
	contentInfoMap, err := s.batchGetContentInfo(ctx, items)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", folderInfo.UserKSUID).
			Str("folder_id", req.FavoriteFolderID).
			Msg("批量获取内容信息失败")
		// 不返回错误，继续处理，只是没有内容详情
	}

	// 批量获取点赞统计信息
	likeStatsMap, err := s.batchGetLikeStats(ctx, items)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", folderInfo.UserKSUID).
			Str("folder_id", req.FavoriteFolderID).
			Msg("批量获取点赞统计信息失败")
		// 不返回错误，继续处理，只是没有点赞统计
	}

	// 转换为响应格式
	var itemResponses []*dto.FavoriteItemResponse
	for _, item := range items {
		itemResponse := s.toItemResponse(item)
		// 添加内容信息
		if contentInfoMap != nil {
			if contentInfo, exists := contentInfoMap[item.ContentKSUID]; exists {
				itemResponse.ContentInfo = &dto.ContentInfo{
					UserKSUID:    contentInfo.UserKSUID,
					ContentKSUID: contentInfo.ContentKSUID,
					Title:        contentInfo.Title,
					CoverURL:     contentInfo.CoverURL,
					Duration:     contentInfo.Duration,
					ViewCount:    contentInfo.ViewCount,
					LikeCount:    0, // 初始化为0，后面会被覆盖
					DislikeCount: 0, // 初始化为0，后面会被覆盖
					CommentCount: contentInfo.CommentCount,
				}

				// 用本服务的点赞统计覆盖 video 服务的点赞数据
				if likeStatsMap != nil {
					if likeStats, exists := likeStatsMap[item.ContentKSUID]; exists {
						itemResponse.ContentInfo.LikeCount = likeStats.LikeCount
						itemResponse.ContentInfo.DislikeCount = likeStats.DislikeCount
					}
				}
			}
		}
		itemResponses = append(itemResponses, itemResponse)
	}

	totalPages := int(math.Ceil(float64(total) / float64(req.PageSize)))

	response := &dto.GetFavoriteItemsResponse{
		Items:      itemResponses,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}

	log.Info().
		Str("user_ksuid", folderInfo.UserKSUID).
		Int64("total", total).
		Int("returned", len(itemResponses)).
		Msg("收藏项列表获取成功")

	return response, nil
}

// ===== 辅助方法 =====



// batchGetContentInfo 批量获取内容信息
func (s *FavoriteItemService) batchGetContentInfo(ctx context.Context, items []*model.FavoriteItem) (map[string]*client.ContentInfo, *errors.Errors) {
	if len(items) == 0 {
		return make(map[string]*client.ContentInfo), nil
	}

	// 按内容类型分组收集内容KSUID
	videoKSUIDs := make([]string, 0)
	novelKSUIDs := make([]string, 0)
	musicKSUIDs := make([]string, 0)

	for _, item := range items {
		switch item.ContentType {
		case "video", "anime", "short":
			videoKSUIDs = append(videoKSUIDs, item.ContentKSUID)
		case "novel":
			novelKSUIDs = append(novelKSUIDs, item.ContentKSUID)
		case "music":
			musicKSUIDs = append(musicKSUIDs, item.ContentKSUID)
		}
	}

	// 合并所有内容信息
	allContentInfo := make(map[string]*client.ContentInfo)

	// 批量获取视频内容信息
	if len(videoKSUIDs) > 0 {
		videoContentInfo, err := s.videoClient.BatchGetContentsByKSUIDs(videoKSUIDs)
		if err != nil {
			log.Error().Err(err).
				Strs("video_ksuids", videoKSUIDs).
				Msg("批量获取视频内容信息失败")
		} else {
			for k, v := range videoContentInfo {
				allContentInfo[k] = v
			}
		}
	}

	// 批量获取小说内容信息
	if len(novelKSUIDs) > 0 {
		novelContentInfo, err := s.novelClient.BatchGetContentsByKSUIDs(novelKSUIDs)
		if err != nil {
			log.Error().Err(err).
				Strs("novel_ksuids", novelKSUIDs).
				Msg("批量获取小说内容信息失败")
		} else {
			for k, v := range novelContentInfo {
				allContentInfo[k] = v
			}
		}
	}

	// 批量获取音乐内容信息
	if len(musicKSUIDs) > 0 {
		musicContentInfo, err := s.musicClient.BatchGetContentsByKSUIDs(musicKSUIDs)
		if err != nil {
			log.Error().Err(err).
				Strs("music_ksuids", musicKSUIDs).
				Msg("批量获取音乐内容信息失败")
		} else {
			for k, v := range musicContentInfo {
				allContentInfo[k] = v
			}
		}
	}

	log.Info().
		Int("total_items", len(items)).
		Int("video_count", len(videoKSUIDs)).
		Int("novel_count", len(novelKSUIDs)).
		Int("music_count", len(musicKSUIDs)).
		Int("content_info_count", len(allContentInfo)).
		Msg("批量获取内容信息完成")

	return allContentInfo, nil
}

// batchGetLikeStats 批量获取点赞统计信息
func (s *FavoriteItemService) batchGetLikeStats(ctx context.Context, items []*model.FavoriteItem) (map[string]repository.LikeStatsItem, *errors.Errors) {
	if len(items) == 0 {
		return make(map[string]repository.LikeStatsItem), nil
	}

	// 收集所有内容KSUID
	contentKSUIDs := make([]string, 0, len(items))
	for _, item := range items {
		contentKSUIDs = append(contentKSUIDs, item.ContentKSUID)
	}

	// 批量获取点赞统计
	likeStatsMap, err := s.likeRepo.GetContentLikeStats(ctx, contentKSUIDs)
	if err != nil {
		log.Error().Err(err).
			Int("content_count", len(contentKSUIDs)).
			Msg("批量获取点赞统计信息失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	log.Info().
		Int("total_items", len(items)).
		Int("like_stats_count", len(likeStatsMap)).
		Msg("批量获取点赞统计信息完成")

	return likeStatsMap, nil
}

// toItemResponse 转换收藏项模型为响应格式
func (s *FavoriteItemService) toItemResponse(item *model.FavoriteItem) *dto.FavoriteItemResponse {
	return &dto.FavoriteItemResponse{
		FavoriteItemID:   item.FavoriteItemID,
		UserKSUID:        item.UserKSUID,
		ContentKSUID:     item.ContentKSUID,
		ContentType:      string(item.ContentType),
		FavoriteFolderID: item.FavoriteFolderID,
		CreatedAt:        item.CreatedAt,
		UpdatedAt:        item.UpdatedAt,
	}
}

// GetTopFavoritedContent 获取最受欢迎的内容
func (s *FavoriteItemService) GetTopFavoritedContent(ctx context.Context, contentType string, limit int) (*dto.GetTopFavoritedContentResponse, *errors.Errors) {
	log.Debug().
		Str("content_type", contentType).
		Int("limit", limit).
		Msg("开始获取最受欢迎的内容")

	var modelContentType model.ContentType
	if contentType != "" {
		modelContentType = model.ContentType(contentType)
		if !model.IsValidContentType(modelContentType) {
			return nil, errors.NewGlobalErrors(errors.INVALID_PARAMETER, errors.INVALID_PARAMETER, fmt.Errorf("无效的内容类型: %s", contentType))
		}
	}

	statsList, err := s.favoriteStatsRepo.GetTopFavoritedContent(ctx, modelContentType, limit)
	if err != nil {
		log.Error().Err(err).
			Str("content_type", contentType).
			Int("limit", limit).
			Msg("获取最受欢迎的内容失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	contents := make([]*dto.ContentFavoriteStatsResponse, 0, len(statsList))
	for _, stats := range statsList {
		contents = append(contents, &dto.ContentFavoriteStatsResponse{
			ContentKSUID:  stats.ContentKSUID,
			ContentType:   string(stats.ContentType),
			FavoriteCount: stats.FavoriteCount,
			CreatedAt:     stats.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:     stats.UpdatedAt.Format("2006-01-02 15:04:05"),
		})
	}

	return &dto.GetTopFavoritedContentResponse{
		Contents: contents,
	}, nil
}
