{"openapi": "3.0.3", "info": {"title": "Blacklist API", "description": "用户黑名单管理API，提供拉黑、取消拉黑、查看黑名单列表和检查黑名单状态等功能", "version": "1.0.0", "contact": {"name": "PXPAT Backend Team", "email": "<EMAIL>"}}, "servers": [{"url": "https://api.pxpat.com", "description": "生产环境"}, {"url": "https://dev-api.pxpat.com", "description": "开发环境"}], "security": [{"BearerAuth": []}], "paths": {"/api/blacklist/block": {"post": {"tags": ["Blacklist"], "summary": "拉黑用户", "description": "将指定用户添加到黑名单，同时删除双方的关注关系", "operationId": "blockUser", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlockUserRequest"}, "example": {"blocked_ksuid": "2SwDAAB2EBtoDWgEHWcBCuMSNDA"}}}}, "responses": {"200": {"description": "拉黑成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/GlobalResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/BlockUserResponse"}}}]}, "example": {"code": 0, "data": {"success": true, "message": "拉黑成功", "blocked_ksuid": "2SwDAAB2EBtoDWgEHWcBCuMSNDA", "is_blocked": true}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "delete": {"tags": ["Blacklist"], "summary": "取消拉黑用户", "description": "将指定用户从黑名单中移除", "operationId": "unblockUser", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnblockUserRequest"}, "example": {"blocked_ksuid": "2SwDAAB2EBtoDWgEHWcBCuMSNDA"}}}}, "responses": {"200": {"description": "取消拉黑成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/GlobalResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/BlockUserResponse"}}}]}, "example": {"code": 0, "data": {"success": true, "message": "取消拉黑成功", "blocked_ksuid": "2SwDAAB2EBtoDWgEHWcBCuMSNDA", "is_blocked": false}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/api/blacklist/": {"get": {"tags": ["Blacklist"], "summary": "获取黑名单列表", "description": "获取当前用户的黑名单用户列表，支持分页", "operationId": "getBlacklist", "security": [{"BearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "页码，从1开始", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}, "example": 1}, {"name": "page_size", "in": "query", "description": "每页数量，最大50", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 50, "default": 20}, "example": 20}], "responses": {"200": {"description": "获取黑名单列表成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/GlobalResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/BlacklistResponse"}}}]}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/api/blacklist/status": {"get": {"tags": ["Blacklist"], "summary": "检查黑名单状态", "description": "检查当前用户与目标用户之间的黑名单关系状态", "operationId": "checkBlacklistStatus", "security": [{"BearerAuth": []}], "parameters": [{"name": "target_ksuid", "in": "query", "description": "目标用户的KSUID", "required": true, "schema": {"type": "string", "pattern": "^[0-9A-Za-z]{27}$"}, "example": "2SwDAAB2EBtoDWgEHWcBCuMSNDA"}], "responses": {"200": {"description": "检查黑名单状态成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/GlobalResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/CheckBlacklistStatusResponse"}}}]}, "example": {"code": 0, "data": {"is_blacklisted": true, "is_blacklisted_by": false}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}}, "components": {"securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT认证令牌，格式：Bearer <token>"}}, "schemas": {"GlobalResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码，0表示成功，其他值表示错误"}, "message": {"type": "string", "description": "响应消息"}}, "required": ["code"]}, "BlockUserRequest": {"type": "object", "properties": {"blocked_ksuid": {"type": "string", "pattern": "^[0-9A-Za-z]{27}$", "description": "被拉黑者的用户KSUID"}}, "required": ["blocked_ksuid"], "example": {"blocked_ksuid": "2SwDAAB2EBtoDWgEHWcBCuMSNDA"}}, "UnblockUserRequest": {"type": "object", "properties": {"blocked_ksuid": {"type": "string", "pattern": "^[0-9A-Za-z]{27}$", "description": "被拉黑者的用户KSUID"}}, "required": ["blocked_ksuid"], "example": {"blocked_ksuid": "2SwDAAB2EBtoDWgEHWcBCuMSNDA"}}, "BlockUserResponse": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功"}, "message": {"type": "string", "description": "响应消息"}, "blocked_ksuid": {"type": "string", "description": "被拉黑者的用户KSUID"}, "is_blocked": {"type": "boolean", "description": "是否已拉黑"}}, "required": ["success", "message", "blocked_ksuid", "is_blocked"]}, "UserBlacklistInfo": {"type": "object", "properties": {"user_ksuid": {"type": "string", "pattern": "^[0-9A-Za-z]{27}$", "description": "用户KSUID"}, "username": {"type": "string", "description": "用户名"}, "nickname": {"type": "string", "description": "昵称"}, "avatar_url": {"type": "string", "format": "uri", "description": "头像URL"}, "user_type": {"type": "string", "description": "用户类型"}, "is_verified": {"type": "boolean", "description": "是否认证"}, "blacklisted_at": {"type": "string", "format": "date-time", "description": "拉黑时间"}}, "required": ["user_ksuid", "username", "nickname", "avatar_url", "user_type", "is_verified", "blacklisted_at"]}, "BlacklistResponse": {"type": "object", "properties": {"users": {"type": "array", "items": {"$ref": "#/components/schemas/UserBlacklistInfo"}, "description": "用户列表"}, "total": {"type": "integer", "format": "int64", "description": "总数"}, "page": {"type": "integer", "description": "当前页码"}, "page_size": {"type": "integer", "description": "每页数量"}, "total_pages": {"type": "integer", "description": "总页数"}}, "required": ["users", "total", "page", "page_size", "total_pages"], "example": {"users": [{"user_ksuid": "2SwDAAB2EBtoDWgEHWcBCuMSNDA", "username": "user123", "nickname": "测试用户", "avatar_url": "https://example.com/avatar.jpg", "user_type": "normal", "is_verified": false, "blacklisted_at": "2024-01-15T10:30:00Z"}], "total": 1, "page": 1, "page_size": 20, "total_pages": 1}}, "CheckBlacklistStatusResponse": {"type": "object", "properties": {"is_blacklisted": {"type": "boolean", "description": "是否已拉黑目标用户"}, "is_blacklisted_by": {"type": "boolean", "description": "是否被目标用户拉黑"}}, "required": ["is_blacklisted", "is_blacklisted_by"]}}, "responses": {"BadRequest": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}, "example": {"code": 10000, "message": "请求参数错误"}}}}, "Unauthorized": {"description": "未授权访问", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}, "example": {"code": 10001, "message": "无效的认证令牌"}}}}, "InternalServerError": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}, "example": {"code": -1, "message": "服务器内部错误"}}}}}}, "tags": [{"name": "Blacklist", "description": "黑名单管理相关API"}]}