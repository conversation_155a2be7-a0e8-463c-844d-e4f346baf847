package service

import (
	"context"
	"fmt"
	"pxpat-backend/internal/user-cluster/user-service/dto"
	"pxpat-backend/internal/user-cluster/user-service/repository"
	"pxpat-backend/pkg/errors"

	"github.com/rs/zerolog/log"
)

// BlacklistService 黑名单服务
type BlacklistService struct {
	blacklistRepo repository.BlacklistRepository
	followRepo    repository.FollowRepository
	userRepo      repository.UserRepository
}

// NewBlacklistService 创建黑名单服务实例
func NewBlacklistService(blacklistRepo repository.BlacklistRepository, followRepo repository.FollowRepository, userRepo repository.UserRepository) *BlacklistService {
	return &BlacklistService{
		blacklistRepo: blacklistRepo,
		followRepo:    followRepo,
		userRepo:      userRepo,
	}
}

// BlockUser 拉黑用户
func (s *BlacklistService) BlockUser(ctx context.Context, blockerKSUID, blockedKSUID string) (*dto.BlockUserResponse, *errors.Errors) {
	log.Info().
		Str("blocker_ksuid", blockerKSUID).
		Str("blocked_ksuid", blockedKSUID).
		Msg("开始处理拉黑用户请求")

	// 验证输入参数
	if blockerKSUID == "" {
		log.Error().Msg("拉黑者用户ID为空")
		return nil, errors.NewGlobalErrors(errors.INVALID_PARAMETER, errors.INVALID_PARAMETER, fmt.Errorf("拉黑者用户ID不能为空"))
	}

	if blockedKSUID == "" {
		log.Error().Msg("被拉黑者用户ID为空")
		return nil, errors.NewGlobalErrors(errors.INVALID_PARAMETER, errors.INVALID_PARAMETER, fmt.Errorf("被拉黑者用户ID不能为空"))
	}

	// 检查是否尝试拉黑自己
	if blockerKSUID == blockedKSUID {
		log.Warn().Str("user_ksuid", blockerKSUID).Msg("用户尝试拉黑自己")
		return nil, errors.NewGlobalErrors(errors.INVALID_PARAMETER, errors.INVALID_PARAMETER, fmt.Errorf("不能拉黑自己"))
	}

	// 检查被拉黑的用户是否存在
	blocked, err := s.userRepo.GetByUserKSUID(ctx, blockedKSUID)
	if err != nil {
		if errors.Is(err, repository.ErrUserNotFound) {
			return &dto.BlockUserResponse{
				Success:      false,
				Message:      "用户不存在",
				BlockedKSUID: blockedKSUID,
				IsBlocked:    false,
			}, nil
		}
		log.Error().Err(err).Str("blocked_ksuid", blockedKSUID).Msg("获取被拉黑用户信息失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	// 检查是否已经拉黑
	isBlacklisted, err := s.blacklistRepo.IsBlacklisted(ctx, blockerKSUID, blockedKSUID)
	if err != nil {
		log.Error().Err(err).Str("blocker_ksuid", blockerKSUID).Str("blocked_ksuid", blockedKSUID).Msg("检查拉黑状态失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	if isBlacklisted {
		return &dto.BlockUserResponse{
			Success:      false,
			Message:      "已经拉黑了该用户",
			BlockedKSUID: blockedKSUID,
			IsBlocked:    true,
		}, nil
	}

	// 拉黑用户并删除关注关系
	err = s.blacklistRepo.BlockUserAndRemoveFollow(ctx, blockerKSUID, blockedKSUID)
	if err != nil {
		log.Error().Err(err).Str("blocker_ksuid", blockerKSUID).Str("blocked_ksuid", blockedKSUID).Msg("拉黑用户失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.CREATE_RECORD_ERROR, err)
	}

	log.Info().
		Str("blocker_ksuid", blockerKSUID).
		Str("blocked_ksuid", blockedKSUID).
		Str("blocked_nickname", blocked.Nickname).
		Msg("拉黑用户成功")

	return &dto.BlockUserResponse{
		Success:      true,
		Message:      fmt.Sprintf("成功拉黑 %s", blocked.Nickname),
		BlockedKSUID: blockedKSUID,
		IsBlocked:    true,
	}, nil
}

// UnblockUser 取消拉黑用户
func (s *BlacklistService) UnblockUser(ctx context.Context, blockerKSUID, blockedKSUID string) (*dto.BlockUserResponse, *errors.Errors) {
	log.Info().
		Str("blocker_ksuid", blockerKSUID).
		Str("blocked_ksuid", blockedKSUID).
		Msg("开始处理取消拉黑用户请求")

	// 验证输入参数
	if blockerKSUID == "" {
		log.Error().Msg("拉黑者用户ID为空")
		return nil, errors.NewGlobalErrors(errors.INVALID_PARAMETER, errors.INVALID_PARAMETER, fmt.Errorf("拉黑者用户ID不能为空"))
	}

	if blockedKSUID == "" {
		log.Error().Msg("被拉黑者用户ID为空")
		return nil, errors.NewGlobalErrors(errors.INVALID_PARAMETER, errors.INVALID_PARAMETER, fmt.Errorf("被拉黑者用户ID不能为空"))
	}

	// 检查是否尝试取消拉黑自己
	if blockerKSUID == blockedKSUID {
		log.Warn().Str("user_ksuid", blockerKSUID).Msg("用户尝试取消拉黑自己")
		return nil, errors.NewGlobalErrors(errors.INVALID_PARAMETER, errors.INVALID_PARAMETER, fmt.Errorf("不能取消拉黑自己"))
	}

	// 检查是否已经拉黑
	isBlacklisted, err := s.blacklistRepo.IsBlacklisted(ctx, blockerKSUID, blockedKSUID)
	if err != nil {
		log.Error().Err(err).Str("blocker_ksuid", blockerKSUID).Str("blocked_ksuid", blockedKSUID).Msg("检查拉黑状态失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	if !isBlacklisted {
		return &dto.BlockUserResponse{
			Success:      false,
			Message:      "未拉黑该用户",
			BlockedKSUID: blockedKSUID,
			IsBlocked:    false,
		}, nil
	}

	// 删除黑名单关系
	err = s.blacklistRepo.DeleteBlacklist(ctx, blockerKSUID, blockedKSUID)
	if err != nil {
		log.Error().Err(err).Str("blocker_ksuid", blockerKSUID).Str("blocked_ksuid", blockedKSUID).Msg("删除黑名单关系失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.DELETE_RECORD_ERROR, err)
	}

	log.Info().
		Str("blocker_ksuid", blockerKSUID).
		Str("blocked_ksuid", blockedKSUID).
		Msg("取消拉黑用户成功")

	return &dto.BlockUserResponse{
		Success:      true,
		Message:      "取消拉黑成功",
		BlockedKSUID: blockedKSUID,
		IsBlocked:    false,
	}, nil
}

// GetBlacklist 获取黑名单列表
func (s *BlacklistService) GetBlacklist(ctx context.Context, userKSUID string, page, pageSize int) (*dto.BlacklistResponse, *errors.Errors) {
	log.Debug().
		Str("user_ksuid", userKSUID).
		Int("page", page).
		Int("page_size", pageSize).
		Msg("开始获取黑名单列表")

	// 验证输入参数
	if userKSUID == "" {
		log.Error().Msg("用户ID为空")
		return nil, errors.NewGlobalErrors(errors.INVALID_PARAMETER, errors.INVALID_PARAMETER, fmt.Errorf("用户ID不能为空"))
	}

	// 设置默认值和边界检查
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}
	if pageSize > 50 {
		log.Warn().Int("requested_page_size", pageSize).Msg("请求的页面大小超过限制，使用默认值")
		pageSize = 50
	}

	blacklistUsers, total, err := s.blacklistRepo.GetBlacklistUsers(ctx, userKSUID, page, pageSize)
	if err != nil {
		log.Error().Err(err).Str("user_ksuid", userKSUID).Msg("获取黑名单列表失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))

	// 转换为UserBlacklistInfo切片
	users := make([]dto.UserBlacklistInfo, len(blacklistUsers))
	for i, user := range blacklistUsers {
		users[i] = *user
	}

	log.Debug().
		Str("user_ksuid", userKSUID).
		Int64("total", total).
		Int("page", page).
		Int("page_size", pageSize).
		Int("returned_count", len(users)).
		Msg("获取黑名单列表成功")

	return &dto.BlacklistResponse{
		Users:      users,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}, nil
}

// CheckBlacklistStatus 检查黑名单状态
func (s *BlacklistService) CheckBlacklistStatus(ctx context.Context, currentUserKSUID, targetUserKSUID string) (*dto.CheckBlacklistStatusResponse, *errors.Errors) {
	log.Debug().
		Str("current_user_ksuid", currentUserKSUID).
		Str("target_user_ksuid", targetUserKSUID).
		Msg("开始检查黑名单状态")

	// 验证输入参数
	if currentUserKSUID == "" {
		log.Error().Msg("当前用户ID为空")
		return nil, errors.NewGlobalErrors(errors.INVALID_PARAMETER, errors.INVALID_PARAMETER, fmt.Errorf("当前用户ID不能为空"))
	}

	if targetUserKSUID == "" {
		log.Error().Msg("目标用户ID为空")
		return nil, errors.NewGlobalErrors(errors.INVALID_PARAMETER, errors.INVALID_PARAMETER, fmt.Errorf("目标用户ID不能为空"))
	}

	// 检查是否拉黑对方
	isBlacklisted, err := s.blacklistRepo.IsBlacklisted(ctx, currentUserKSUID, targetUserKSUID)
	if err != nil {
		log.Error().Err(err).Str("current_user_ksuid", currentUserKSUID).Str("target_user_ksuid", targetUserKSUID).Msg("检查拉黑状态失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	// 检查是否被对方拉黑
	isBlacklistedBy, err := s.blacklistRepo.IsBlacklisted(ctx, targetUserKSUID, currentUserKSUID)
	if err != nil {
		log.Error().Err(err).Str("current_user_ksuid", currentUserKSUID).Str("target_user_ksuid", targetUserKSUID).Msg("检查被拉黑状态失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	log.Debug().
		Str("current_user_ksuid", currentUserKSUID).
		Str("target_user_ksuid", targetUserKSUID).
		Bool("is_blacklisted", isBlacklisted).
		Bool("is_blacklisted_by", isBlacklistedBy).
		Msg("检查黑名单状态成功")

	return &dto.CheckBlacklistStatusResponse{
		IsBlacklisted:   isBlacklisted,
		IsBlacklistedBy: isBlacklistedBy,
	}, nil
}
