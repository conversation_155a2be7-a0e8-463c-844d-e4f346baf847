package handler

import (
	"context"
	"net/http"
	"pxpat-backend/internal/user-cluster/user-service/dto"
	"pxpat-backend/pkg/errors"
	"pxpat-backend/pkg/middleware/tracing"
	globalTypes "pxpat-backend/pkg/types"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
)

// BlacklistServiceInterface 黑名单服务接口
type BlacklistServiceInterface interface {
	CheckBlacklistStatus(ctx context.Context, currentUserKSUID, targetUserKSUID string) (*dto.CheckBlacklistStatusResponse, *errors.Errors)
	BlockUser(ctx context.Context, blockerKSUID, blockedKSUID string) (*dto.BlockUserResponse, *errors.Errors)
	UnblockUser(ctx context.Context, blockerKSUID, blockedKSUID string) (*dto.BlockUserResponse, *errors.Errors)
	GetBlacklist(ctx context.Context, userKSUID string, page, pageSize int) (*dto.BlacklistResponse, *errors.Errors)
}

// InternalBlacklistHandler 内部黑名单服务处理器
type InternalBlacklistHandler struct {
	blacklistService BlacklistServiceInterface
}

// NewInternalBlacklistHandler 创建内部黑名单处理器实例
func NewInternalBlacklistHandler(blacklistService BlacklistServiceInterface) *InternalBlacklistHandler {
	return &InternalBlacklistHandler{
		blacklistService: blacklistService,
	}
}

// CheckBlacklistStatusForService 检查黑名单状态（内部接口）
func (h *InternalBlacklistHandler) CheckBlacklistStatusForService(c *gin.Context) {
	startTime := time.Now()
	
	// 获取trace信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)
	
	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("method", c.Request.Method).
		Str("path", c.Request.URL.Path).
		Str("client_ip", c.ClientIP()).
		Msg("开始处理内部检查黑名单状态请求")

	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "CheckBlacklistStatusForService")
	defer span.End()

	// 绑定参数
	var req dto.InternalCheckBlacklistStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().
			Err(err).
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Str("path", c.Request.URL.Path).
			Str("method", c.Request.Method).
			Str("client_ip", c.ClientIP()).
			Dur("duration", time.Since(startTime)).
			Msg("内部检查黑名单状态请求参数绑定失败")

		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code:    errors.INVALID_PARAMETER,
			Message: "请求参数无效",
			Data:    nil,
		})
		return
	}

	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("current_user_ksuid", req.CurrentUserKSUID).
		Str("target_user_ksuid", req.TargetUserKSUID).
		Msg("内部检查黑名单状态参数")

	// 调用服务层
	response, gErr := h.blacklistService.CheckBlacklistStatus(ctx, req.CurrentUserKSUID, req.TargetUserKSUID)
	if gErr != nil {
		log.Error().
			Err(gErr).
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Str("current_user_ksuid", req.CurrentUserKSUID).
			Str("target_user_ksuid", req.TargetUserKSUID).
			Str("client_ip", c.ClientIP()).
			Dur("duration", time.Since(startTime)).
			Msg("内部检查黑名单状态失败")

		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("current_user_ksuid", req.CurrentUserKSUID).
		Str("target_user_ksuid", req.TargetUserKSUID).
		Bool("is_blacklisted", response.IsBlacklisted).
		Bool("is_blacklisted_by", response.IsBlacklistedBy).
		Str("client_ip", c.ClientIP()).
		Dur("duration", time.Since(startTime)).
		Msg("内部检查黑名单状态成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: response,
	})
}
