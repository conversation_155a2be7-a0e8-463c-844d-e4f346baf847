package blacklist

import (
	"github.com/gin-gonic/gin"
	"pxpat-backend/internal/user-cluster/user-service/external/handler"
)

// RegisterBlacklistExternalRoutes 注册黑名单功能的外部路由
func RegisterBlacklistExternalRoutes(r *gin.RouterGroup, blacklistHandler *handler.BlacklistHandler, authMiddleware gin.HandlerFunc) {
	// 黑名单相关路由组
	blacklistGroup := r.Group("/blacklist")

	// 需要认证的路由
	blacklistGroup.Use(authMiddleware)
	{
		// 拉黑用户
		blacklistGroup.POST("/block", blacklistHandler.BlockUser)

		// 取消拉黑用户
		blacklistGroup.DELETE("/block", blacklistHandler.UnblockUser)

		// 获取黑名单列表
		blacklistGroup.GET("/", blacklistHandler.GetBlacklist)
	}
}
