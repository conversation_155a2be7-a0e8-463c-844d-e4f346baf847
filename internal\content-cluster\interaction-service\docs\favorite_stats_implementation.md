# 收藏统计功能实现文档

## 概述

为 favorite 功能增加了 favorite_stats 统计功能，实现了每个用户收藏就给那个内容计数一次的需求。即使用户将内容收藏到多个收藏夹，也只计数一次。只有当用户删除所有收藏夹中的该内容时，才会减少统计计数。

## 实现的功能

### 1. 数据模型

#### FavoriteStats 模型 (`model/favorite_stats.go`)
- `stats_ksuid`: 统计记录的唯一标识
- `content_ksuid`: 内容ID（唯一索引）
- `content_type`: 内容类型
- `favorite_count`: 收藏数量（去重用户）
- `created_at`, `updated_at`: 时间戳

#### 主要方法
- `NewFavoriteStats()`: 创建新的统计记录
- `IncrementFavorite()`: 增加收藏数量
- `DecrementFavorite()`: 减少收藏数量
- `UpdateCount()`: 更新收藏数量
- `IsPopular()`: 判断是否受欢迎（≥100收藏）
- `IsHot()`: 判断是否热门（≥1000收藏）

### 2. 数据访问层

#### FavoriteStatsRepository 接口 (`repository/favorite_interfaces.go`)
- `CreateOrUpdateStats()`: 创建或更新统计
- `GetStatsByContentKSUID()`: 获取单个内容统计
- `BatchGetStatsByContentKSUIDs()`: 批量获取统计
- `IncrementFavoriteCount()`: 增加收藏计数
- `DecrementFavoriteCount()`: 减少收藏计数
- `GetTopFavoritedContent()`: 获取热门内容
- `GetStatsByContentType()`: 按类型获取统计
- `RefreshStats()`: 刷新统计数据
- `DeleteStats()`: 删除统计记录
- `GetUniqueUserCount()`: 获取唯一用户数量

#### 实现 (`repository/impl/favorite_stats_repository_impl.go`)
- 完整实现了所有接口方法
- 支持事务操作
- 包含详细的日志记录
- 错误处理完善

### 3. 业务逻辑层

#### FavoriteItemService 扩展 (`external/service/favorite_item_service.go`)

##### 统计更新逻辑
- **添加收藏时**: 检查用户是否第一次收藏该内容，如果是则增加统计
- **删除收藏时**: 检查用户是否完全取消收藏该内容，如果是则减少统计

##### 新增方法
- `GetContentFavoriteStats()`: 获取内容收藏统计
- `BatchGetContentFavoriteStats()`: 批量获取统计
- `GetTopFavoritedContent()`: 获取最受欢迎内容

### 4. API 接口

#### 新增 DTO (`dto/favorite_dto.go`)
- `ContentFavoriteStatsResponse`: 内容收藏统计响应
- `GetContentFavoriteStatsRequest`: 获取统计请求
- `BatchGetContentFavoriteStatsRequest`: 批量获取请求
- `GetTopFavoritedContentRequest`: 获取热门内容请求

#### 新增 API 端点 (`external/handler/favorite_item_handler.go`)
- `GET /api/v1/interaction/favorite/content-stats`: 获取内容收藏统计
- `POST /api/v1/interaction/favorite/content-stats/batch`: 批量获取统计
- `GET /api/v1/interaction/favorite/public/top-favorited`: 获取最受欢迎内容

### 5. 数据库迁移

#### 表结构 (`migrations/migrate.go`)
```sql
CREATE TABLE interaction_favorite_stats (
    stats_ksuid VARCHAR(32) PRIMARY KEY,
    content_ksuid VARCHAR(32) NOT NULL UNIQUE,
    content_type VARCHAR(10) NOT NULL,
    favorite_count BIGINT NOT NULL DEFAULT 0,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    deleted_at TIMESTAMP
);

-- 索引
CREATE INDEX idx_favorite_stats_content_type ON interaction_favorite_stats(content_type);
CREATE INDEX idx_favorite_stats_favorite_count ON interaction_favorite_stats(favorite_count);
CREATE INDEX idx_favorite_stats_created_at ON interaction_favorite_stats(created_at);
```

### 6. 依赖注入

#### 更新的提供者 (`cmd/content-cluster/interaction-service/main.go`)
- `provideFavoriteStatsRepository()`: 提供统计仓储
- 更新 `provideFavoriteItemService()`: 注入统计仓储
- 更新依赖注入配置

## 统计逻辑说明

### 收藏计数规则
1. **用户第一次收藏内容**: 统计 +1
2. **用户将已收藏内容添加到其他收藏夹**: 统计不变
3. **用户从某个收藏夹删除内容，但其他收藏夹仍有**: 统计不变
4. **用户删除所有收藏夹中的内容**: 统计 -1

### 数据一致性
- 使用事务确保数据一致性
- 统计失败不影响收藏操作的成功
- 提供刷新统计功能以修复数据不一致

## 测试

### 单元测试 (`repository/impl/favorite_stats_repository_test.go`)
- 测试模型的基本功能
- 测试内容类型验证
- 测试表名配置

### 编译验证
- 项目编译成功
- 所有依赖正确注入
- API 路由配置正确

## 使用示例

### 获取内容收藏统计
```bash
GET /api/v1/interaction/favorite/content-stats?content_ksuid=01ARZ3NDEKTSV4RRFFQ69G5FAV
```

### 批量获取统计
```bash
POST /api/v1/interaction/favorite/content-stats/batch
{
  "content_ksuids": ["01ARZ3NDEKTSV4RRFFQ69G5FAV", "01ARZ3NDEKTSV4RRFFQ69G5FAW"]
}
```

### 获取热门内容
```bash
GET /api/v1/interaction/favorite/public/top-favorited?content_type=video&limit=10
```

## 注意事项

1. **性能考虑**: 统计表有适当的索引，支持高效查询
2. **扩展性**: 支持按内容类型过滤和分页
3. **容错性**: 统计操作失败不影响主要业务流程
4. **数据修复**: 提供刷新统计功能以处理数据不一致情况

## 修复的问题

### 收藏夹item_count不更新问题

**问题描述**: 当用户添加收藏内容时，favorite_folders表中收藏夹的item_count字段没有增加。

**根本原因**:
1. AddToFavorite方法只创建了收藏项，但没有更新收藏夹的item_count
2. RemoveFromFavorite方法删除收藏项时也没有更新item_count
3. MoveFavoriteItem方法移动收藏项时没有更新源和目标收藏夹的item_count
4. DeleteFolder方法删除收藏夹时没有处理其中的收藏项和统计

**修复方案**:

#### 1. AddToFavorite方法修复
- 在成功创建收藏项后，调用`UpdateItemCount(ctx, folderID, true)`增加收藏夹计数
- 为每个添加的收藏夹都更新计数

#### 2. RemoveFromFavorite方法修复
- 在删除前记录受影响的收藏夹ID
- 删除收藏项后，为每个受影响的收藏夹调用`UpdateItemCount(ctx, folderID, false)`减少计数

#### 3. MoveFavoriteItem方法修复
- 获取原始收藏项信息，确定源收藏夹ID
- 检查是否是同一个收藏夹，避免无意义的移动
- 移动成功后，源收藏夹计数-1，目标收藏夹计数+1

#### 4. DeleteFolder方法修复
- 删除收藏夹前先获取其中的所有收藏项
- 删除收藏项并更新favorite_stats统计
- 检查每个内容是否在用户的其他收藏夹中存在，如果不存在则减少统计
- 最后删除收藏夹

#### 5. FavoriteFolderService依赖注入修复
- 添加FavoriteStatsRepository依赖
- 更新构造函数和main.go中的提供者

### 修复的文件

1. **external/service/favorite_item_service.go**
   - AddToFavorite: 添加收藏夹计数更新
   - RemoveFromFavorite: 添加收藏夹计数更新
   - MoveFavoriteItem: 完善移动逻辑和计数更新

2. **external/service/favorite_folder_service.go**
   - 添加FavoriteStatsRepository依赖
   - DeleteFolder: 完善删除逻辑，处理收藏项和统计

3. **cmd/content-cluster/interaction-service/main.go**
   - 更新FavoriteFolderService提供者，注入FavoriteStatsRepository

### 数据一致性保证

1. **错误处理**: 统计更新失败不影响主要业务操作
2. **事务安全**: 使用数据库事务确保操作的原子性
3. **日志记录**: 详细记录操作过程，便于问题排查
4. **幂等性**: 避免重复计数和无效操作

## 后续优化建议

1. 考虑使用缓存提高热门内容查询性能
2. 定期任务验证和修复统计数据
3. 监控统计数据的准确性
4. 考虑分布式环境下的并发安全性
5. 添加数据一致性检查工具
