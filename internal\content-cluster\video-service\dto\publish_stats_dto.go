package dto

import (
	"pxpat-backend/internal/content-cluster/video-service/model"
	"pxpat-backend/pkg/errors"
)

// GetUsersByCategoryRequest 根据分类获取用户请求
type GetUsersByCategoryRequest struct {
	CategoryID uint `form:"category_id" json:"category_id" example:"1"` // 分类ID，可选，不传则按粉丝数排序
	Page       int  `form:"page" json:"page" example:"1"`               // 页码，从1开始，默认1
	PageSize   int  `form:"page_size" json:"page_size" example:"20"`    // 每页数量，默认20，最大100
}

// Validate 验证请求参数
func (req *GetUsersByCategoryRequest) Validate() *errors.Errors {
	// CategoryID 现在是可选的，不需要验证是否为0

	if req.Page <= 0 {
		req.Page = 1
	}

	if req.PageSize <= 0 || req.PageSize > 20 {
		req.PageSize = 20
	}

	return nil
}

// GetUsersByCategoryResponse 根据分类获取用户响应
type GetUsersByCategoryResponse struct {
	Users      []*PublishStatsInfo `json:"users"`       // 用户统计列表
	Total      int64               `json:"total"`       // 总数量
	Page       int                 `json:"page"`        // 当前页码
	PageSize   int                 `json:"page_size"`   // 每页数量
	TotalPages int                 `json:"total_pages"` // 总页数
	HasNext    bool                `json:"has_next"`    // 是否有下一页
	HasPrev    bool                `json:"has_prev"`    // 是否有上一页
}

// PublishStatsInfo 发布统计信息
type PublishStatsInfo struct {
	UserKSUID    string         `json:"user_ksuid"`    // 用户KSUID
	MainCategory uint           `json:"main_category"` // 主分类ID
	FollowersCount int64        `json:"followers_count"` // 粉丝数量
	UserInfo     *UserBasicInfo `json:"user_info"`     // 用户基本信息
}

// ConvertToPublishStatsInfo 将模型转换为DTO
func ConvertToPublishStatsInfo(stats *model.PublishStats) *PublishStatsInfo {
	info := &PublishStatsInfo{
		UserKSUID:      stats.UserKSUID,
		MainCategory:   stats.MainCategory,
		FollowersCount: stats.FollowersCount,
	}

	return info
}

// ConvertToPublishStatsInfoListWithUsers 将模型列表转换为DTO列表，并合并用户信息
func ConvertToPublishStatsInfoListWithUsers(statsList []*model.PublishStats, usersMap map[string]*UserBasicInfo) []*PublishStatsInfo {
	result := make([]*PublishStatsInfo, 0, len(statsList))
	for _, stats := range statsList {
		info := ConvertToPublishStatsInfo(stats)
		// 添加用户信息
		if userInfo, exists := usersMap[stats.UserKSUID]; exists {
			info.UserInfo = userInfo
		}
		result = append(result, info)
	}
	return result
}

// CalculatePagination 计算分页信息
func CalculatePagination(total int64, page, pageSize int) (totalPages int, hasNext, hasPrev bool) {
	if pageSize <= 0 {
		pageSize = 20
	}

	totalPages = int((total + int64(pageSize) - 1) / int64(pageSize))
	if totalPages == 0 {
		totalPages = 1
	}

	hasNext = page < totalPages
	hasPrev = page > 1

	return totalPages, hasNext, hasPrev
}
