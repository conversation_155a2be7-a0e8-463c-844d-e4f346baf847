package repository

import (
	"context"
	"errors"
	"pxpat-backend/internal/user-cluster/user-service/dto"
	"pxpat-backend/internal/user-cluster/user-service/model"
)

// 定义关注相关错误
var (
	ErrFollowNotFound       = errors.New("follow relationship not found")
	ErrAlreadyFollowing     = errors.New("already following this user")
	ErrCannotFollowSelf     = errors.New("cannot follow yourself")
	ErrFollowRelationExists = errors.New("follow relation already exists")
)

// FollowRepository 关注仓库接口
type FollowRepository interface {
	// CreateFollow 创建关注关系
	CreateFollow(ctx context.Context, follow *model.UserFollow) error

	// DeleteFollow 删除关注关系（硬删除）
	DeleteFollow(ctx context.Context, followerKSUID, followeeKSUID string) error

	// GetFollow 获取关注关系
	GetFollow(ctx context.Context, followerKSUID, followeeKSUID string) (*model.UserFollow, error)

	// IsFollowing 检查是否已关注
	IsFollowing(ctx context.Context, followerKSUID, followeeKSUID string) (bool, error)

	// GetFollowers 获取粉丝列表
	GetFollowers(ctx context.Context, userKSUID string, page, pageSize int) ([]*dto.UserFollowInfo, int64, error)

	// GetFollowing 获取关注列表
	GetFollowing(ctx context.Context, userKSUID string, page, pageSize int) ([]*dto.UserFollowInfo, int64, error)

	// GetFollowersCount 获取粉丝数量
	GetFollowersCount(ctx context.Context, userKSUID string) (int64, error)

	// GetFollowingCount 获取关注数量
	GetFollowingCount(ctx context.Context, userKSUID string) (int64, error)

	// UpdateUserFollowCounts 更新用户的关注和粉丝数量
	UpdateUserFollowCounts(ctx context.Context, userKSUID string) error

	// GetMutualFollows 获取互相关注的用户列表
	GetMutualFollows(ctx context.Context, userKSUID string, page, pageSize int) ([]*dto.UserFollowInfo, int64, error)

	// GetFollowStats 获取关注/被关注统计信息
	GetFollowStats(ctx context.Context, userKSUID string) (*dto.FollowStatsResponse, error)

	// BatchCheckFollowStatus 批量检查关注状态
	BatchCheckFollowStatus(ctx context.Context, currentUserKSUID string, targetUserKSUIDs []string) ([]dto.UserFollowStatus, error)
}
