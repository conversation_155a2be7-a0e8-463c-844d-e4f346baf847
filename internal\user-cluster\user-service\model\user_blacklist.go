package model

import (
	"time"
)

// UserBlacklist 用户黑名单模型
type UserBlacklist struct {
	ID           int        `json:"id" gorm:"primaryKey;autoIncrement;type:bigserial"`                  // 自增主键ID
	BlockerKSUID string     `json:"blocker_ksuid" gorm:"column:blocker_ksuid;not null;type:char(27)"`  // 拉黑者的用户KSUID
	BlockedKSUID string     `json:"blocked_ksuid" gorm:"column:blocked_ksuid;not null;type:char(27)"`  // 被拉黑者的用户KSUID
	CreatedAt    time.Time  `json:"created_at" gorm:"type:timestamp"`                                  // 拉黑时间
	UpdatedAt    time.Time  `json:"updated_at" gorm:"type:timestamp"`                                  // 更新时间
	DeletedAt    *time.Time `json:"deleted_at,omitempty" gorm:"index;type:timestamp"`                  // 软删除时间标记

	// 关联关系
	Blocker *User `json:"blocker,omitempty" gorm:"foreignKey:BlockerKSUID;references:UserKSUID"` // 拉黑者信息
	Blocked *User `json:"blocked,omitempty" gorm:"foreignKey:BlockedKSUID;references:UserKSUID"` // 被拉黑者信息
}

// TableName 指定表名
func (*UserBlacklist) TableName() string {
	return "user_blacklists"
}

// NewUserBlacklist 创建新的黑名单关系
func NewUserBlacklist(blockerKSUID, blockedKSUID string) *UserBlacklist {
	return &UserBlacklist{
		BlockerKSUID: blockerKSUID,
		BlockedKSUID: blockedKSUID,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}
}
