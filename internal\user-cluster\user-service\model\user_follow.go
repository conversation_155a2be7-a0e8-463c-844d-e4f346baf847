package model

import (
	"time"
)

// UserFollow 用户关注关系模型
type UserFollow struct {
	ID            int        `json:"id" gorm:"primaryKey;autoIncrement;type:bigserial"`                  // 自增主键ID
	FollowerKSUID string     `json:"follower_ksuid" gorm:"column:follower_ksuid;not null;type:char(27)"` // 关注者的用户KSUID
	FolloweeKSUID string     `json:"followee_ksuid" gorm:"column:followee_ksuid;not null;type:char(27)"` // 被关注者的用户KSUID
	Status        string     `json:"status" gorm:"default:'active';type:varchar(16)"`                    // 关注状态 (active: 正常关注, blocked: 被屏蔽)
	CreatedAt     time.Time  `json:"created_at" gorm:"type:timestamp"`                                   // 关注时间
	UpdatedAt     time.Time  `json:"updated_at" gorm:"type:timestamp"`                                   // 更新时间
	DeletedAt     *time.Time `json:"deleted_at,omitempty" gorm:"index;type:timestamp"`                   // 软删除时间标记

	// 关联关系
	Follower *User `json:"follower,omitempty" gorm:"foreignKey:FollowerKSUID;references:UserKSUID"` // 关注者信息
	Followee *User `json:"followee,omitempty" gorm:"foreignKey:FolloweeKSUID;references:UserKSUID"` // 被关注者信息
}

// 关注状态常量
const (
	FollowStatusActive  = "active"  // 正常关注
	FollowStatusBlocked = "blocked" // 被屏蔽
)

// TableName 指定表名
func (*UserFollow) TableName() string {
	return "user_follows"
}

// NewUserFollow 创建新的关注关系
func NewUserFollow(followerKSUID, followeeKSUID string) *UserFollow {
	return &UserFollow{
		FollowerKSUID: followerKSUID,
		FolloweeKSUID: followeeKSUID,
		Status:        FollowStatusActive,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}
}

// IsActive 检查关注关系是否为活跃状态
func (uf *UserFollow) IsActive() bool {
	return uf.Status == FollowStatusActive && uf.DeletedAt == nil
}

// Block 屏蔽关注关系
func (uf *UserFollow) Block() {
	uf.Status = FollowStatusBlocked
	uf.UpdatedAt = time.Now()
}

// Unblock 取消屏蔽关注关系
func (uf *UserFollow) Unblock() {
	uf.Status = FollowStatusActive
	uf.UpdatedAt = time.Now()
}
