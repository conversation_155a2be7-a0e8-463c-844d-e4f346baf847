package handler

import (
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"net/http"
	"pxpat-backend/internal/user-cluster/user-service/dto"
	"pxpat-backend/internal/user-cluster/user-service/external/service"
	"pxpat-backend/pkg/errors"
	"pxpat-backend/pkg/ksuid"
	"pxpat-backend/pkg/middleware/tracing"
	globalTypes "pxpat-backend/pkg/types"
)

// BlacklistHandler 黑名单功能HTTP处理器
type BlacklistHandler struct {
	blacklistService *service.BlacklistService
}

// NewBlacklistHandler 创建黑名单处理器实例
func NewBlacklistHandler(blacklistService *service.BlacklistService) *BlacklistHandler {
	return &BlacklistHandler{
		blacklistService: blacklistService,
	}
}

// BlockUser 拉黑用户
func (h *BlacklistHandler) BlockUser(c *gin.Context) {
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "BlockUser")
	defer span.End()

	// 获取当前用户KSUID
	currentUserKSUID := ksuid.GetKSUID(c)

	// 解析请求参数
	var req dto.BlockUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 调用服务层
	response, gErr := h.blacklistService.BlockUser(ctx, currentUserKSUID, req.BlockedKSUID)
	if gErr != nil {
		log.Error().Err(gErr).Str("blocker_ksuid", currentUserKSUID).Str("blocked_ksuid", req.BlockedKSUID).Msg("拉黑用户失败")

		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: response,
	})
}

// UnblockUser 取消拉黑用户
func (h *BlacklistHandler) UnblockUser(c *gin.Context) {
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "UnblockUser")
	defer span.End()

	// 获取当前用户KSUID
	currentUserKSUID := ksuid.GetKSUID(c)

	// 解析请求参数
	var req dto.UnblockUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 调用服务层
	response, gErr := h.blacklistService.UnblockUser(ctx, currentUserKSUID, req.BlockedKSUID)
	if gErr != nil {
		log.Error().Err(gErr).Str("blocker_ksuid", currentUserKSUID).Str("blocked_ksuid", req.BlockedKSUID).Msg("取消拉黑用户失败")

		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: response,
	})
}

// GetBlacklist 获取黑名单列表
func (h *BlacklistHandler) GetBlacklist(c *gin.Context) {
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "GetBlacklist")
	defer span.End()

	// 获取当前用户KSUID
	currentUserKSUID := ksuid.GetKSUID(c)

	// 解析请求参数
	var req dto.GetBlacklistRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 调用服务层
	response, gErr := h.blacklistService.GetBlacklist(ctx, currentUserKSUID, req.Page, req.PageSize)
	if gErr != nil {
		log.Error().Err(gErr).
			Str("user_ksuid", currentUserKSUID).
			Msg("获取黑名单列表失败")

		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: response,
	})
}
