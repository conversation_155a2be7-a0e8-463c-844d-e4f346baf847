package handler

import (
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"net/http"
	"pxpat-backend/internal/content-cluster/interaction-service/dto"
	"pxpat-backend/internal/content-cluster/interaction-service/external/service"
	"pxpat-backend/pkg/errors"
	"pxpat-backend/pkg/ksuid"
	globalTypes "pxpat-backend/pkg/types"
)

// FavoriteItemHandler 收藏项处理器
type FavoriteItemHandler struct {
	favoriteItemService *service.FavoriteItemService
}

// NewFavoriteItemHandler 创建收藏项处理器
func NewFavoriteItemHandler(favoriteItemService *service.FavoriteItemService) *FavoriteItemHandler {
	return &FavoriteItemHandler{
		favoriteItemService: favoriteItemService,
	}
}







// ManageFavorite 管理收藏（合并添加和删除功能）
func (h *FavoriteItemHandler) ManageFavorite(c *gin.Context) {
	userKSUID := ksuid.GetKSUID(c)

	var req dto.ManageFavoriteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Msg("管理收藏请求参数无效")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 参数验证
	if req.ContentKSUID == "" {
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 检查是否有操作
	if len(req.AddFavoriteFolderIDs) == 0 && len(req.DelFavoriteFolderIDs) == 0 {
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	result, gErr := h.favoriteItemService.ManageFavorite(c.Request.Context(), userKSUID, &req)
	if gErr != nil {
		log.Error().Err(gErr).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("管理收藏失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Int("add_count", len(req.AddFavoriteFolderIDs)).
		Int("del_count", len(req.DelFavoriteFolderIDs)).
		Msg("管理收藏成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: result,
	})
}

// GetFavoriteItems 获取收藏项列表
func (h *FavoriteItemHandler) GetFavoriteItems(c *gin.Context) {
	userKSUID := ksuid.GetKSUID(c)

	// 解析查询参数
	var req dto.GetFavoriteItemsRequest
	err := c.ShouldBindQuery(&req)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Msg("获取收藏项列表请求参数无效")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	if req.SortBy == "" {
		req.SortBy = "created_at"
	}
	if req.SortOrder == "" {
		req.SortOrder = "desc"
	}

	response, gErr := h.favoriteItemService.GetFavoriteItems(c.Request.Context(), userKSUID, &req)
	if gErr != nil {
		log.Error().Err(gErr).
			Str("user_ksuid", userKSUID).
			Str("folder_id", req.FavoriteFolderID).
			Msg("获取收藏项列表失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: response,
	})
}

// GetTopFavoritedContent 获取最受欢迎的内容
func (h *FavoriteItemHandler) GetTopFavoritedContent(c *gin.Context) {
	// 解析查询参数
	var req dto.GetTopFavoritedContentRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error().Err(err).
			Msg("获取热门收藏内容请求参数无效")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 设置默认值
	if req.Limit <= 0 || req.Limit > 100 {
		req.Limit = 10
	}

	response, gErr := h.favoriteItemService.GetTopFavoritedContent(c.Request.Context(), req.ContentType, req.Limit)
	if gErr != nil {
		log.Error().Err(gErr).
			Str("content_type", req.ContentType).
			Int("limit", req.Limit).
			Msg("获取最受欢迎的内容失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: response,
	})
}
