package handler

import (
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"net/http"
	"pxpat-backend/internal/user-cluster/user-service/dto"
	"pxpat-backend/internal/user-cluster/user-service/external/service"
	"pxpat-backend/pkg/errors"
	"pxpat-backend/pkg/ksuid"
	"pxpat-backend/pkg/middleware/tracing"
	globalTypes "pxpat-backend/pkg/types"
)

// FollowHandler 关注功能HTTP处理器
type FollowHandler struct {
	followService *service.FollowService
}

// NewFollowHandler 创建关注处理器实例
func NewFollowHandler(followService *service.FollowService) *FollowHandler {
	return &FollowHandler{
		followService: followService,
	}
}

// FollowUser 关注用户
func (h *FollowHandler) FollowUser(c *gin.Context) {
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "FollowUser")
	defer span.End()

	// 获取当前用户KSUID
	currentUserKSUID := ksuid.GetKSUID(c)

	// 解析请求参数
	var req dto.FollowUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 调用服务层
	response, gErr := h.followService.FollowUser(ctx, currentUserKSUID, req.FolloweeKSUID)
	if gErr != nil {
		log.Error().Err(gErr).Str("follower_ksuid", currentUserKSUID).Str("followee_ksuid", req.FolloweeKSUID).Msg("关注用户失败")

		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: response,
	})
}

// UnfollowUser 取消关注用户
func (h *FollowHandler) UnfollowUser(c *gin.Context) {
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "UnfollowUser")
	defer span.End()

	// 获取当前用户KSUID
	currentUserKSUID := ksuid.GetKSUID(c)

	// 解析请求参数
	var req dto.UnfollowUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 调用服务层
	response, gErr := h.followService.UnfollowUser(ctx, currentUserKSUID, req.FolloweeKSUID)
	if gErr != nil {
		log.Error().Err(gErr).Str("follower_ksuid", currentUserKSUID).Str("followee_ksuid", req.FolloweeKSUID).Msg("取消关注用户失败")

		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: response,
	})
}

// GetFollowers 获取粉丝列表
func (h *FollowHandler) GetFollowers(c *gin.Context) {
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "GetFollowers")
	defer span.End()

	// 解析请求参数
	var req dto.GetFollowersRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 获取当前用户信息（如果已登录）
	currentUserKSUID, exist := ksuid.TryGetKSUID(c)

	reqUserKSUID := req.UserKSUID

	// 确定要查询的用户KSUID
	if reqUserKSUID == "" {
		// 如果没有指定用户KSUID，则获取当前用户的粉丝
		if !exist {
			c.JSON(http.StatusUnauthorized, globalTypes.GlobalResponse{
				Code: errors.INVALID_PARAMETER,
			})
			return
		}
		reqUserKSUID = currentUserKSUID
	}

	// 检查隐私权限
	if reqUserKSUID != currentUserKSUID {
		// 查看其他用户的粉丝列表，需要检查隐私设置
		response, gErr := h.followService.CheckFollowersPrivacy(ctx, reqUserKSUID)
		if gErr != nil {
			log.Error().Err(gErr).
				Str("req_userKSUID", reqUserKSUID).
				Str("current_userKSUID", currentUserKSUID).
				Msg("检查粉丝列表隐私设置失败")

			c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
				Code: gErr.Code,
			})
			return
		}

		if !response {
			c.JSON(http.StatusForbidden, globalTypes.GlobalResponse{
				Code: errors.PERMISSION_DENIED,
			})
			return
		}
	}

	// 调用服务层
	response, gErr := h.followService.GetFollowers(ctx, reqUserKSUID, req.Page, req.PageSize)
	if gErr != nil {
		log.Error().Err(gErr).
			Str("req_userKSUID", reqUserKSUID).
			Str("current_userKSUID", currentUserKSUID).
			Msg("检查粉丝列表隐私设置失败")

		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: response,
	})
}

// GetFollowing 获取关注列表
func (h *FollowHandler) GetFollowing(c *gin.Context) {
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "GetFollowing")
	defer span.End()

	// 解析请求参数
	var req dto.GetFollowingRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 获取当前用户信息（如果已登录）
	currentUserKSUID, exist := ksuid.TryGetKSUID(c)

	reqUserKSUID := req.UserKSUID

	// 确定要查询的用户KSUID
	if reqUserKSUID == "" {
		// 如果没有指定用户KSUID，则获取当前用户的粉丝
		if !exist {
			c.JSON(http.StatusUnauthorized, globalTypes.GlobalResponse{
				Code: errors.INVALID_PARAMETER,
			})
			return
		}
		reqUserKSUID = currentUserKSUID
	}

	// 检查隐私权限
	if reqUserKSUID != currentUserKSUID {
		// 查看其他用户的关注列表，需要检查隐私设置
		response, gErr := h.followService.CheckFollowingPrivacy(ctx, reqUserKSUID)
		if gErr != nil {
			log.Error().Err(gErr).
				Str("req_userKSUID", reqUserKSUID).
				Str("current_userKSUID", currentUserKSUID).
				Msg("检查关注列表隐私设置失败")

			c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
				Code: gErr.Code,
			})
			return
		}

		if !response {
			c.JSON(http.StatusForbidden, globalTypes.GlobalResponse{
				Code: errors.SECRET_USER_FAVORITE,
			})
			return
		}
	}

	// 调用服务层
	response, gErr := h.followService.GetFollowing(ctx, reqUserKSUID, req.Page, req.PageSize)
	if gErr != nil {
		log.Error().Err(gErr).
			Str("req_userKSUID", reqUserKSUID).
			Str("current_userKSUID", currentUserKSUID).
			Msg("获取关注列表失败")

		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: response,
	})
}

// CheckFollowStatus 检查关注状态
func (h *FollowHandler) CheckFollowStatus(c *gin.Context) {
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "CheckFollowStatus")
	defer span.End()

	// 获取当前用户KSUID
	currentUserKSUID, exists := ksuid.TryGetKSUID(c)
	if !exists {
		c.JSON(http.StatusOK, globalTypes.GlobalResponse{
			Code: errors.SUCCESS,
			Data: dto.CheckFollowStatusResponse{
				IsFollowing:    false,
				IsFollowedBy:   false,
				IsMutualFollow: false,
			},
		})
		return
	}

	// 解析请求参数
	var req dto.CheckFollowStatusRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 调用服务层
	response, gErr := h.followService.CheckFollowStatus(ctx, currentUserKSUID, req.FolloweeKSUID)
	if gErr != nil {
		log.Error().Err(gErr).
			Str("current_user", currentUserKSUID).
			Str("target_user", req.FolloweeKSUID).
			Msg("检查关注状态失败")

		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: response,
	})
}

// GetFollowStats 获取关注/被关注统计信息
func (h *FollowHandler) GetFollowStats(c *gin.Context) {
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "GetFollowStats")
	defer span.End()

	// 获取用户KSUID参数
	userKSUID := c.Query("user_ksuid")

	// 如果没有指定用户KSUID，则获取当前用户的统计
	if userKSUID == "" {
		currentUserKSUID, exists := ksuid.TryGetKSUID(c)
		if !exists {
			log.Warn().Str("path", c.Request.URL.Path).Msg("用户未认证或KSUID为空")
			c.JSON(http.StatusUnauthorized, globalTypes.GlobalResponse{
				Code: errors.INVALID_PARAMETER,
			})
			return
		}
		userKSUID = currentUserKSUID
	}

	// 调用服务层
	response, gErr := h.followService.GetFollowStats(ctx, userKSUID)
	if gErr != nil {
		log.Error().Err(gErr).
			Str("user_ksuid", userKSUID).
			Msg("获取关注统计失败")

		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: response,
	})
}


