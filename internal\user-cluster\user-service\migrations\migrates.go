package migrations

import (
	"log"

	"gorm.io/gorm"
	"pxpat-backend/internal/user-cluster/user-service/model"
)

// Migrates 进行数据库迁移
// 控制整个服务的数据库迁移
func Migrates(db *gorm.DB) error {
	err := db.AutoMigrate(
		// 用户相关模型
		&model.User{},
		&model.UserAlias{},     // 添加用户别名模型
		&model.UserFollow{},    // 添加用户关注模型
		&model.UserBlacklist{}, // 添加用户黑名单模型
		&model.Verification{},
		&model.Role{},
		&model.RolePermission{},
		&model.ExperienceRecord{}, // 添加经验值记录模型
		&model.ReputationRecord{}, // 添加声望记录模型

	)
	if err != nil {
		return err
	}

	// 创建额外的索引
	if err := createAdditionalIndexes(db); err != nil {
		log.Printf("创建额外索引时出现错误: %v", err)
		// 不返回错误，继续执行，因为索引创建失败不应该阻止服务启动
	}

	return nil
}

// createAdditionalIndexes 创建额外的索引
func createAdditionalIndexes(db *gorm.DB) error {
	log.Println("开始创建用户服务额外索引...")

	// 用户关注表索引
	indexes := []string{
		// 基础索引
		"CREATE INDEX IF NOT EXISTS idx_user_follows_follower ON user_follows(follower_ksuid)",
		"CREATE INDEX IF NOT EXISTS idx_user_follows_followee ON user_follows(followee_ksuid)",
		"CREATE INDEX IF NOT EXISTS idx_user_follows_deleted_at ON user_follows(deleted_at)",
		"CREATE INDEX IF NOT EXISTS idx_user_follows_status ON user_follows(status)",
		"CREATE INDEX IF NOT EXISTS idx_user_follows_created_at ON user_follows(created_at DESC)",

		// 复合索引
		"CREATE INDEX IF NOT EXISTS idx_user_follows_follower_status ON user_follows(follower_ksuid, status)",
		"CREATE INDEX IF NOT EXISTS idx_user_follows_followee_status ON user_follows(followee_ksuid, status)",
		"CREATE INDEX IF NOT EXISTS idx_user_follows_status_created ON user_follows(status, created_at DESC)",

		// 唯一索引：防止重复关注（排除软删除的记录）
		"CREATE UNIQUE INDEX IF NOT EXISTS idx_user_follows_unique ON user_follows(follower_ksuid, followee_ksuid) WHERE deleted_at IS NULL",

		// 用户表索引（为关注功能相关字段）
		"CREATE INDEX IF NOT EXISTS idx_user_users_is_following_public ON user_users(is_following_public)",
		"CREATE INDEX IF NOT EXISTS idx_user_users_is_follower_public ON user_users(is_follower_public)",
		"CREATE INDEX IF NOT EXISTS idx_user_users_followers_count ON user_users(followers_count DESC)",
		"CREATE INDEX IF NOT EXISTS idx_user_users_following_count ON user_users(following_count DESC)",
	}

	for _, indexSQL := range indexes {
		if err := db.Exec(indexSQL).Error; err != nil {
			log.Printf("创建索引失败: %s, 错误: %v", indexSQL, err)
			// 继续执行其他索引，不中断迁移过程
		} else {
			log.Printf("索引创建成功: %s", indexSQL)
		}
	}

	log.Println("用户服务额外索引创建完成")
	return nil
}
