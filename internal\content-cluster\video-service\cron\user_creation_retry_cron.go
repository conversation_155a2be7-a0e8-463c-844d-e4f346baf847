package cron

import (
	"context"
	"encoding/json"
	"fmt"

	"pxpat-backend/internal/content-cluster/video-service/client"
	"pxpat-backend/internal/content-cluster/video-service/model"
	"pxpat-backend/internal/content-cluster/video-service/repository"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

// UserCreationRetryCron 用户创建重试定时任务
type UserCreationRetryCron struct {
	db                   *gorm.DB
	retryRepo            *repository.UserCreationRetryRepository
	userServiceClient    client.UserServiceClient
	batchSize            int
	maxConcurrentRetries int
}

// NewUserCreationRetryCron 创建用户创建重试定时任务
func NewUserCreationRetryCron(db *gorm.DB, userServiceClient client.UserServiceClient) *UserCreationRetryCron {
	return &UserCreationRetryCron{
		db:                   db,
		retryRepo:            repository.NewUserCreationRetryRepository(db),
		userServiceClient:    userServiceClient,
		batchSize:            50, // 每次处理50个任务
		maxConcurrentRetries: 10, // 最大并发重试数
	}
}

// ProcessRetryTasks 处理重试任务的主要方法
func (c *UserCreationRetryCron) ProcessRetryTasks() {
	ctx := context.Background()

	log.Info().Msg("开始执行用户创建重试任务")

	// 获取可重试的任务
	tasks, err := c.retryRepo.GetRetryableTasks(ctx, c.batchSize)
	if err != nil {
		log.Error().
			Err(err).
			Msg("获取可重试任务失败")
		return
	}

	if len(tasks) == 0 {
		log.Debug().Msg("没有需要重试的任务")
		return
	}

	log.Info().
		Int("task_count", len(tasks)).
		Msg("找到需要重试的任务")

	// 使用信号量控制并发数
	semaphore := make(chan struct{}, c.maxConcurrentRetries)

	// 处理每个任务
	for _, task := range tasks {
		semaphore <- struct{}{} // 获取信号量

		go func(t *model.UserCreationRetryTask) {
			defer func() { <-semaphore }() // 释放信号量
			c.processRetryTask(ctx, t)
		}(task)
	}

	// 等待所有任务完成
	for i := 0; i < c.maxConcurrentRetries; i++ {
		semaphore <- struct{}{}
	}

	log.Info().
		Int("processed_count", len(tasks)).
		Msg("用户创建重试任务处理完成")
}

// processRetryTask 处理单个重试任务
func (c *UserCreationRetryCron) processRetryTask(ctx context.Context, task *model.UserCreationRetryTask) {
	log.Info().
		Uint("task_id", task.ID).
		Str("person_name", task.PersonName).
		Str("person_type", task.PersonType).
		Int("retry_count", task.RetryCount).
		Msg("开始处理重试任务")

	// 解析用户创建数据
	var userData repository.UserCreationData
	if err := json.Unmarshal([]byte(task.UserCreationData), &userData); err != nil {
		log.Error().
			Err(err).
			Uint("task_id", task.ID).
			Msg("解析用户创建数据失败")
		c.retryRepo.UpdateTaskAfterRetry(ctx, task, false, fmt.Errorf("解析用户创建数据失败: %w", err))
		return
	}

	// 构建批量创建用户请求
	userInfo := client.BatchCreateUserInfo{
		Email:    userData.Email,
		Nickname: userData.Nickname,
		RealName: userData.RealName,
		UserType: userData.UserType,
		Bio:      userData.Bio,
		Region:   userData.Region,
		Roles:    userData.Roles,
	}

	req := &client.BatchCreateUsersRequest{
		Users: []client.BatchCreateUserInfo{userInfo},
	}

	// 调用用户服务批量创建用户
	resp, err := c.userServiceClient.BatchCreateUsers(req)
	if err != nil {
		log.Error().
			Err(err).
			Uint("task_id", task.ID).
			Str("person_name", task.PersonName).
			Msg("调用用户服务批量创建用户失败")
		c.retryRepo.UpdateTaskAfterRetry(ctx, task, false, fmt.Errorf("调用用户服务失败: %w", err))
		return
	}

	// 检查创建结果
	if resp.SuccessCount == 0 || len(resp.Results) == 0 {
		err := fmt.Errorf("批量创建用户失败，成功数量: %d", resp.SuccessCount)
		log.Error().
			Uint("task_id", task.ID).
			Str("person_name", task.PersonName).
			Int("success_count", resp.SuccessCount).
			Int("failed_count", resp.FailedCount).
			Msg("批量创建用户失败")
		c.retryRepo.UpdateTaskAfterRetry(ctx, task, false, err)
		return
	}

	result := resp.Results[0]
	if !result.Success {
		err := fmt.Errorf("创建用户失败: %s", result.Error)
		log.Error().
			Uint("task_id", task.ID).
			Str("person_name", task.PersonName).
			Str("error", result.Error).
			Msg("创建用户失败")
		c.retryRepo.UpdateTaskAfterRetry(ctx, task, false, err)
		return
	}

	log.Info().
		Uint("task_id", task.ID).
		Str("person_name", task.PersonName).
		Str("user_ksuid", result.UserKSUID).
		Str("action", result.Action).
		Msg("用户创建重试成功")

	// 为新创建的用户添加别名
	if result.Action == "created" {
		if err := c.userServiceClient.CreateUserAlias(result.UserKSUID, task.PersonName); err != nil {
			log.Error().
				Err(err).
				Uint("task_id", task.ID).
				Str("person_name", task.PersonName).
				Str("user_ksuid", result.UserKSUID).
				Msg("为新用户创建别名失败")
			// 不标记为失败，因为用户已经创建成功，别名创建失败不影响主流程
		} else {
			log.Info().
				Uint("task_id", task.ID).
				Str("person_name", task.PersonName).
				Str("user_ksuid", result.UserKSUID).
				Msg("为新用户创建别名成功")
		}
	}

	// 标记任务为成功
	if err := c.retryRepo.UpdateTaskAfterRetry(ctx, task, true, nil); err != nil {
		log.Error().
			Err(err).
			Uint("task_id", task.ID).
			Msg("更新任务状态为成功失败")
	}
}

// CleanupOldTasks 清理旧任务
func (c *UserCreationRetryCron) CleanupOldTasks() {
	ctx := context.Background()

	log.Info().Msg("开始清理旧的用户创建重试任务")

	if err := c.retryRepo.CleanupOldTasks(ctx); err != nil {
		log.Error().
			Err(err).
			Msg("清理旧任务失败")
		return
	}

	log.Info().Msg("旧任务清理完成")
}

// GetTaskStats 获取任务统计信息
func (c *UserCreationRetryCron) GetTaskStats() {
	ctx := context.Background()

	stats, err := c.retryRepo.GetTaskStats(ctx)
	if err != nil {
		log.Error().
			Err(err).
			Msg("获取任务统计信息失败")
		return
	}

	log.Info().
		Int64("pending", stats["pending"]).
		Int64("success", stats["success"]).
		Int64("failed", stats["failed"]).
		Msg("用户创建重试任务统计")
}
