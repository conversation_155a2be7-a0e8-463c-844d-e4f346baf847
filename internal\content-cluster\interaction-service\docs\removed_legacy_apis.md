# 已删除的旧收藏API接口

## 概述

为了简化API结构并提高性能，我们删除了原有的三个收藏相关接口，统一使用新的管理收藏接口。

## 删除的接口列表

### 1. 添加到收藏夹接口
- **路由**: `POST /api/v1/favorites/items`
- **Handler方法**: `AddToFavorite`
- **Service方法**: `AddToFavorite`
- **DTO结构体**: `AddToFavoriteRequest`

### 2. 从收藏夹删除接口
- **路由**: `DELETE /api/v1/favorites/items`
- **Handler方法**: `RemoveFromFavorite`
- **Service方法**: `RemoveFromFavorite`
- **DTO结构体**: `RemoveFromFavoriteRequest`

### 3. 移动收藏项接口
- **路由**: `PUT /api/v1/favorites/items/move`
- **Handler方法**: `MoveFavoriteItem`
- **Service方法**: `MoveFavoriteItem`
- **DTO结构体**: `MoveFavoriteItemRequest`

## 删除的辅助方法

### Service层辅助方法
- `checkDuplicateFavorites`: 检查重复收藏的辅助方法，已被新的管理收藏方法内置逻辑替代

## 删除的文件和代码位置

### 路由文件
- **文件**: `internal/content-cluster/interaction-service/routes/favorite/external.go`
- **删除内容**: 
  ```go
  items.POST("", favoriteItemHandler.AddToFavorite)        // 添加到收藏夹
  items.DELETE("", favoriteItemHandler.RemoveFromFavorite) // 从收藏夹移除
  items.PUT("/move", favoriteItemHandler.MoveFavoriteItem) // 移动收藏项
  ```

### Handler文件
- **文件**: `internal/content-cluster/interaction-service/external/handler/favorite_item_handler.go`
- **删除方法**:
  - `AddToFavorite` (约44行代码)
  - `RemoveFromFavorite` (约33行代码)
  - `MoveFavoriteItem` (约43行代码)

### Service文件
- **文件**: `internal/content-cluster/interaction-service/external/service/favorite_item_service.go`
- **删除方法**:
  - `AddToFavorite` (约122行代码)
  - `RemoveFromFavorite` (约135行代码)
  - `MoveFavoriteItem` (约91行代码)
  - `checkDuplicateFavorites` (约24行代码)

### DTO文件
- **文件**: `internal/content-cluster/interaction-service/dto/favorite_dto.go`
- **删除结构体**:
  - `AddToFavoriteRequest`
  - `RemoveFromFavoriteRequest`
  - `MoveFavoriteItemRequest`

## 替代方案

所有被删除的功能现在都通过统一的管理收藏接口实现：

### 新的统一接口
- **路由**: `PUT /api/v1/favorites/items/manage`
- **Handler方法**: `ManageFavorite`
- **Service方法**: `ManageFavorite`
- **DTO结构体**: `ManageFavoriteRequest`

### 功能映射

#### 原添加到收藏夹 → 新管理收藏
```json
// 旧接口
POST /api/v1/favorites/items
{
  "content_ksuid": "01ARZ3NDEKTSV4RRFFQ69G5FAV",
  "content_type": "video",
  "favorite_folder_ids": ["folder_1", "folder_2"]
}

// 新接口
PUT /api/v1/favorites/items/manage
{
  "content_ksuid": "01ARZ3NDEKTSV4RRFFQ69G5FAV",
  "content_type": "video",
  "add_favorite_folder_ids": ["folder_1", "folder_2"],
  "del_favorite_folder_ids": []
}
```

#### 原从收藏夹删除 → 新管理收藏
```json
// 旧接口
DELETE /api/v1/favorites/items
{
  "content_ksuid": "01ARZ3NDEKTSV4RRFFQ69G5FAV",
  "favorite_folder_ids": ["folder_1", "folder_2"]
}

// 新接口
PUT /api/v1/favorites/items/manage
{
  "content_ksuid": "01ARZ3NDEKTSV4RRFFQ69G5FAV",
  "content_type": "video",
  "add_favorite_folder_ids": [],
  "del_favorite_folder_ids": ["folder_1", "folder_2"]
}
```

#### 原移动收藏项 → 新管理收藏
```json
// 旧接口（移动到新收藏夹）
PUT /api/v1/favorites/items/move
{
  "content_ksuid": "01ARZ3NDEKTSV4RRFFQ69G5FAV",
  "target_folder_id": "folder_2"
}

// 新接口（从folder_1移动到folder_2）
PUT /api/v1/favorites/items/manage
{
  "content_ksuid": "01ARZ3NDEKTSV4RRFFQ69G5FAV",
  "content_type": "video",
  "add_favorite_folder_ids": ["folder_2"],
  "del_favorite_folder_ids": ["folder_1"]
}
```

## 优势

### 1. 代码简化
- 删除了约450行重复和冗余代码
- 统一了收藏管理逻辑
- 减少了维护成本

### 2. 性能提升
- 批量操作减少数据库访问次数
- 智能跳过重复操作
- 优化的事务处理

### 3. API简化
- 从3个接口简化为1个接口
- 统一的请求格式
- 更好的错误处理

### 4. 功能增强
- 支持同时添加和删除操作
- 更详细的操作结果反馈
- 更好的日志记录

## 注意事项

1. **不兼容旧版本**: 删除旧接口后，使用旧接口的客户端需要更新
2. **数据库操作**: 新接口的数据库操作逻辑更加优化
3. **错误处理**: 新接口提供了更详细的错误信息和操作结果
4. **日志记录**: 新接口的日志记录更加完善，便于问题排查

## 迁移指南

如果有客户端仍在使用旧接口，需要按照以下方式迁移：

1. **添加收藏**: 使用`add_favorite_folder_ids`参数，`del_favorite_folder_ids`设为空数组
2. **删除收藏**: 使用`del_favorite_folder_ids`参数，`add_favorite_folder_ids`设为空数组
3. **移动收藏**: 同时使用`add_favorite_folder_ids`和`del_favorite_folder_ids`参数
4. **批量操作**: 可以在一次请求中完成多个收藏夹的添加和删除操作

所有操作都需要提供`content_type`参数，这是新接口的必需参数。
