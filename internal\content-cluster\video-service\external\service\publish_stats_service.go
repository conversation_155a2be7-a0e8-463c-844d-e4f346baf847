package service

import (
	"context"

	"github.com/rs/zerolog/log"
	"pxpat-backend/internal/content-cluster/video-service/client"
	"pxpat-backend/internal/content-cluster/video-service/dto"
	"pxpat-backend/internal/content-cluster/video-service/model"
	"pxpat-backend/internal/content-cluster/video-service/repository"
	"pxpat-backend/pkg/errors"
)

// PublishStatsService 发布统计服务
type PublishStatsService struct {
	publishStatsRepo  *repository.PublishStatsRepository
	userServiceClient client.UserServiceClient
}

// NewPublishStatsService 创建发布统计服务
func NewPublishStatsService(publishStatsRepo *repository.PublishStatsRepository, userServiceClient client.UserServiceClient) *PublishStatsService {
	return &PublishStatsService{
		publishStatsRepo:  publishStatsRepo,
		userServiceClient: userServiceClient,
	}
}

// GetUsersByCategory 根据分类获取用户列表
func (s *PublishStatsService) GetUsersByCategory(ctx context.Context, req *dto.GetUsersByCategoryRequest) (*dto.GetUsersByCategoryResponse, *errors.Errors) {
	log.Info().
		Interface("category_id", req.CategoryID).
		Int("page", req.Page).
		Int("page_size", req.PageSize).
		Msg("开始根据分类获取用户列表")

	// 验证请求参数
	if err := req.Validate(); err != nil {
		log.Warn().
			Interface("category_id", req.CategoryID).
			Int("page", req.Page).
			Int("page_size", req.PageSize).
			Msg("请求参数验证失败")
		return nil, err
	}

	// 计算偏移量
	offset := (req.Page - 1) * req.PageSize

	var statsList []*model.PublishStats
	var total int64
	var err error

	// 根据是否传入 category_id 调用不同的方法
	if req.CategoryID > 0 {
		// 按分类获取用户
		statsList, total, err = s.publishStatsRepo.GetUsersByCategory(ctx, req.CategoryID, req.PageSize, offset)
	} else {
		// 按粉丝数排序获取所有用户
		statsList, total, err = s.publishStatsRepo.GetTopUsersByFollowers(ctx, req.PageSize, offset)
	}

	if err != nil {
		log.Error().
			Err(err).
			Interface("category_id", req.CategoryID).
			Int("page", req.Page).
			Int("page_size", req.PageSize).
			Msg("获取用户列表失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	// 提取用户KSUID列表
	userKSUIDs := make([]string, len(statsList))
	for i, stats := range statsList {
		userKSUIDs[i] = stats.UserKSUID
	}

	// 批量获取用户信息
	var usersMap map[string]*dto.UserBasicInfo
	if len(userKSUIDs) > 0 {
		userResponse, err := s.userServiceClient.BatchGetUsers(userKSUIDs)
		usersMap = make(map[string]*dto.UserBasicInfo)
		if err != nil {
			log.Warn().
				Err(err).
				Strs("user_ksuids", userKSUIDs).
				Msg("批量获取用户信息失败，将继续返回不包含用户信息的数据")
		} else {
			// 转换用户信息格式
			for _, user := range userResponse.Users {
				usersMap[user.UserKSUID] = &dto.UserBasicInfo{
					UserKSUID:   user.UserKSUID,
					Username:    user.Username,
					Nickname:    user.Nickname,
					Avatar:      user.Avatar,
					Bio:         user.Bio,
					HideComment: user.HideComment,
				}
			}
		}
	}

	// 转换为 DTO，并合并用户信息
	users := dto.ConvertToPublishStatsInfoListWithUsers(statsList, usersMap)

	// 计算分页信息
	totalPages, hasNext, hasPrev := dto.CalculatePagination(total, req.Page, req.PageSize)

	response := &dto.GetUsersByCategoryResponse{
		Users:      users,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	}

	log.Info().
		Interface("category_id", req.CategoryID).
		Int("page", req.Page).
		Int("page_size", req.PageSize).
		Int64("total", total).
		Int("returned_count", len(users)).
		Msg("根据分类获取用户列表成功")

	return response, nil
}
