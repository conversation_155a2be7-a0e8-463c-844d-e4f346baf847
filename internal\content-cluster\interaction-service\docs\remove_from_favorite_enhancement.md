# 收藏删除功能增强文档

## 概述

增强了收藏删除功能，允许前端指定要从哪个收藏夹删除内容，如果不指定则默认从用户拥有的所有收藏夹删除。

## 功能特性

### 1. 灵活的删除方式

#### 指定收藏夹删除
- 前端可以传递`favorite_folder_ids`数组，指定要从哪些收藏夹删除内容
- 只会删除指定收藏夹中的收藏项
- 如果指定的收藏夹中没有该内容，会跳过并记录日志

#### 全部删除（默认行为）
- 如果不传递`favorite_folder_ids`或传递空数组，则从所有收藏夹删除
- 保持向后兼容性，现有API调用不受影响

### 2. 数据一致性保证

#### 收藏夹计数更新
- 删除收藏项后自动更新对应收藏夹的`item_count`
- 只更新实际受影响的收藏夹

#### 收藏统计更新
- 只有当用户完全取消收藏（所有收藏夹都没有该内容）时，才减少`favorite_stats`统计
- 部分删除不影响全局统计

## 实现细节

### 1. DTO 修改

#### RemoveFromFavoriteRequest 增强
```go
type RemoveFromFavoriteRequest struct {
    ContentKSUID      string   `json:"content_ksuid" binding:"required"`    // 内容ID
    FavoriteFolderIDs []string `json:"favorite_folder_ids,omitempty"`       // 收藏夹ID数组，为空则从所有收藏夹删除
}
```

### 2. Repository 层增强

#### 新增方法
```go
// DeleteByContentAndFolders 从指定收藏夹删除内容
DeleteByContentAndFolders(ctx context.Context, userKSUID, contentKSUID string, folderIDs []string) error
```

#### 实现特点
- 使用`WHERE user_ksuid = ? AND content_ksuid = ? AND favorite_folder_id IN ?`精确删除
- 支持批量删除多个收藏夹中的同一内容
- 详细的日志记录和错误处理

### 3. Service 层重构

#### RemoveFromFavorite 方法增强
1. **参数验证**: 检查用户是否收藏了该内容
2. **删除策略判断**: 根据是否传递收藏夹ID选择删除策略
3. **收藏夹验证**: 验证指定的收藏夹中确实有该内容
4. **执行删除**: 调用相应的repository方法
5. **更新计数**: 更新受影响收藏夹的item_count
6. **统计更新**: 检查是否需要更新全局收藏统计

## API 使用示例

### 从指定收藏夹删除
```bash
DELETE /api/v1/interaction/favorite/items
Content-Type: application/json

{
  "content_ksuid": "01ARZ3NDEKTSV4RRFFQ69G5FAV",
  "favorite_folder_ids": ["folder1", "folder2"]
}
```

### 从所有收藏夹删除（默认行为）
```bash
DELETE /api/v1/interaction/favorite/items
Content-Type: application/json

{
  "content_ksuid": "01ARZ3NDEKTSV4RRFFQ69G5FAV"
}
```

或者

```bash
DELETE /api/v1/interaction/favorite/items
Content-Type: application/json

{
  "content_ksuid": "01ARZ3NDEKTSV4RRFFQ69G5FAV",
  "favorite_folder_ids": []
}
```

## 业务逻辑流程

### 指定收藏夹删除流程
1. 获取用户当前收藏的所有该内容的收藏项
2. 验证指定的收藏夹ID中哪些确实包含该内容
3. 从验证通过的收藏夹中删除收藏项
4. 更新受影响收藏夹的item_count
5. 检查用户是否还有其他收藏夹包含该内容
6. 如果完全没有了，更新favorite_stats统计

### 全部删除流程
1. 获取用户当前收藏的所有该内容的收藏项
2. 删除所有收藏项
3. 更新所有受影响收藏夹的item_count
4. 更新favorite_stats统计（减1）

## 错误处理

### 容错机制
- 统计更新失败不影响主要删除操作
- 收藏夹计数更新失败不影响删除操作
- 详细的错误日志记录

### 边界情况处理
- 用户未收藏该内容：直接返回成功
- 指定的收藏夹中没有该内容：跳过并记录日志
- 空的收藏夹ID数组：按全部删除处理

## 向后兼容性

- 现有API调用方式完全兼容
- 不传递`favorite_folder_ids`字段的行为与之前完全一致
- 不影响现有的前端代码

## 性能优化

### 数据库查询优化
- 使用`IN`查询批量删除多个收藏夹中的内容
- 分表查询基于用户ID，查询效率高
- 避免不必要的数据库操作

### 日志优化
- 结构化日志记录，便于问题排查
- 关键操作步骤都有日志记录
- 包含操作结果统计信息

## 测试建议

### 功能测试
1. 测试从单个收藏夹删除
2. 测试从多个收藏夹删除
3. 测试从所有收藏夹删除
4. 测试删除不存在的内容
5. 测试指定不存在的收藏夹

### 数据一致性测试
1. 验证收藏夹item_count正确更新
2. 验证favorite_stats统计正确更新
3. 验证部分删除不影响全局统计

### 边界情况测试
1. 空收藏夹ID数组
2. 无效的收藏夹ID
3. 用户无权限的收藏夹ID

## 监控指标

### 业务指标
- 指定收藏夹删除vs全部删除的比例
- 删除操作的成功率
- 统计更新的成功率

### 性能指标
- 删除操作的响应时间
- 数据库查询次数
- 错误率统计
