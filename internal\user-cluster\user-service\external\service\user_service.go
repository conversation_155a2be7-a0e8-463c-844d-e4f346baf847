package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	NotifyExternalDTO "pxpat-backend/internal/notify-cluster/external-service/dto"
	"pxpat-backend/internal/user-cluster/user-service/client"
	"pxpat-backend/internal/user-cluster/user-service/dto"
	"pxpat-backend/internal/user-cluster/user-service/messaging/publisher"
	model "pxpat-backend/internal/user-cluster/user-service/model"
	repository2 "pxpat-backend/internal/user-cluster/user-service/repository"
	"pxpat-backend/internal/user-cluster/user-service/types"
	"pxpat-backend/pkg/auth"
	"pxpat-backend/pkg/ksuid"
	"pxpat-backend/pkg/password"
	"pxpat-backend/pkg/region"
	"strings"
	"time"

	"github.com/dgrijalva/jwt-go"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/rs/zerolog/log"
	"pxpat-backend/internal/user-cluster/user-service/utils"
)

// 定义错误
var (
	ErrInvalidCredentials = errors.New("invalid credentials")
	ErrEmailExists        = errors.New("email already exists")
	ErrInviteCodeInvalid  = errors.New("invalid invite code")
)

// CreateDefaultFolderFunc 创建默认收藏夹的回调函数类型
type CreateDefaultFolderFunc func(ctx context.Context, userKSUID string) error

// UserService 用户服务结构体
type UserService struct {
	userRepo                repository2.UserRepository
	roleRepo                repository2.RoleRepository
	verificationRepo        repository2.VerificationRepository
	regionDetector          region.RegionDetector
	pointClient             PointServiceClient
	config                  *types.Config
	jwtSecret               string
	jwtExpireTime           time.Duration
	authManager             auth.Manager
	eventPublisher          *publisher.Publisher
	createDefaultFolderFunc CreateDefaultFolderFunc // 创建默认收藏夹的回调函数
}

// NewUserService 创建用户服务实例
func NewUserService(
	userRepo repository2.UserRepository,
	roleRepo repository2.RoleRepository,
	verificationRepo repository2.VerificationRepository,
	regionDetector region.RegionDetector,
	pointClient PointServiceClient,
	config *types.Config,
	authManager auth.Manager,
) *UserService {
	// 创建事件发布器
	eventPublisher, err := publisher.NewPublisher(config.RabbitMQ.URL)
	if err != nil {
		log.Error().Err(err).Msg("初始化事件发布器失败")
	}

	return &UserService{
		userRepo:                userRepo,
		roleRepo:                roleRepo,
		verificationRepo:        verificationRepo,
		regionDetector:          regionDetector,
		pointClient:             pointClient,
		config:                  config,
		jwtSecret:               config.JWT.SecretKey,
		jwtExpireTime:           config.JWT.Expiration,
		authManager:             authManager,
		eventPublisher:          eventPublisher,
		createDefaultFolderFunc: nil, // 初始为nil，后续通过SetCreateDefaultFolderFunc设置
	}
}

// SetCreateDefaultFolderFunc 设置创建默认收藏夹的回调函数
func (s *UserService) SetCreateDefaultFolderFunc(fn CreateDefaultFolderFunc) {
	s.createDefaultFolderFunc = fn
}

// getNotifyServiceClient 获取通知服务客户端
func (s *UserService) getNotifyServiceClient() client.NotifyServiceClient {
	notifyBaseURL := fmt.Sprintf("http://%s:%d", s.config.Server.AllClusterList.NotifyCluster.Host, s.config.Server.AllClusterList.NotifyCluster.Port)
	return client.NewNotifyServiceClient(client.NotifyServiceConfig{
		BaseURL: notifyBaseURL,
		Timeout: 30 * time.Second,
	})
}

func (s *UserService) SendRegisterEmail(ctx context.Context, req *dto.SendRegisterEmailRequest) (*dto.SendRegisterMailResponse, error) {
	// 获取trace信息
	traceID, spanID := utils.GetTraceInfoFromContext(ctx)

	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("email", req.Email).
		Str("ip", req.Ip).
		Msg("开始处理发送注册邮件服务请求")

	// 检查邮箱是否已存在
	exists, err := s.userRepo.IsEmailExists(req.Email)
	if err != nil {
		log.Error().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Err(err).
			Str("email", req.Email).
			Msg("检查邮箱是否存在失败")
		return nil, fmt.Errorf("failed to check email: %v", err)
	}

	if exists {
		log.Warn().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Str("email", req.Email).
			Str("ip", req.Ip).
			Msg("邮箱已存在，拒绝发送注册邮件")
		return nil, fmt.Errorf("email already exists")
	}

	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("email", req.Email).
		Msg("邮箱检查通过，开始生成验证码")

	// 生成6位数字验证码
	code := make([]byte, 6)
	for i := range code {
		code[i] = byte(rand.Intn(10) + '0')
	}

	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("email", req.Email).
		Str("code_length", "6").
		Msg("验证码生成成功")

	replaceVariable, _ := json.Marshal(map[string]interface{}{
		"name": "test",
		"code": string(code),
	})

	data := NotifyExternalDTO.CreateNotifyExternalRequest{
		Type:          "mail",
		TemplateCode:  "test",
		Title:         "注册邮件",
		To:            req.Email,
		HtmlContent:   string(replaceVariable),
		TextContent:   "这是一封注册邮件",
		OriginUserID:  -1,
		ReceiveUserID: -2,
		Strategy:      "immediate",
		CronTime:      "",
	}

	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("email", req.Email).
		Str("template_code", data.TemplateCode).
		Str("strategy", data.Strategy).
		Str("notify_host", s.config.Server.AllClusterList.NotifyCluster.Host).
		Int("notify_port", s.config.Server.AllClusterList.NotifyCluster.Port).
		Msg("准备调用通知服务发送邮件")

	// 使用通知服务客户端发送邮件
	notifyClient := s.getNotifyServiceClient()
	err = notifyClient.CreateNotification(&data)
	if err != nil {
		log.Error().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Err(err).
			Str("email", req.Email).
			Msg("调用通知服务失败")

		return &dto.SendRegisterMailResponse{
			Success: false,
			Message: err.Error(),
		}, err
	}

	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("email", req.Email).
		Msg("通知服务调用成功，开始保存验证码")

	// 保存验证码到Redis
	cacheKey := fmt.Sprintf("reg@%s#%s", req.Ip, req.Email)
	err = s.userRepo.SetRegisterCodeWithKey(ctx, cacheKey, string(code))
	if err != nil {
		log.Error().
			Err(err).
			Str("email", req.Email).
			Str("cache_key", cacheKey).
			Msg("保存验证码到缓存失败")
		return nil, err
	}

	log.Info().
		Str("email", req.Email).
		Str("ip", req.Ip).
		Str("cache_key", cacheKey).
		Msg("验证码保存成功，注册邮件发送完成")

	return &dto.SendRegisterMailResponse{Success: true, Message: "邮件发送成功"}, nil
}

// Register 处理用户注册
func (s *UserService) Register(ctx context.Context, req *dto.RegisterRequest) (*dto.RegisterResponse, error) {
	// 获取trace信息
	traceID, spanID := utils.GetTraceInfoFromContext(ctx)

	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("email", req.Email).
		Str("nickname", req.Nickname).
		Str("domain", req.Domain).
		Str("ip", req.Ip).
		Str("invite_code", req.InviteCode).
		Msg("开始处理用户注册服务请求")

	// 检查邮箱是否已存在
	exists, err := s.userRepo.IsEmailExists(req.Email)
	if err != nil {
		log.Error().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Err(err).
			Str("email", req.Email).
			Msg("检查邮箱是否存在失败")
		return nil, fmt.Errorf("failed to check email: %v", err)
	}

	if exists {
		log.Warn().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Str("email", req.Email).
			Str("ip", req.Ip).
			Msg("邮箱已存在，拒绝注册")
		return nil, fmt.Errorf("email already exists")
	}

	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("email", req.Email).
		Msg("邮箱检查通过")

	// 获取邀请人用户
	var inviterID string
	// TODO 暂时注释邀请码校验
	//if req.InviteCode != "" {
	//	inviter, err := s.userRepo.GetUserByInviteCode(req.InviteCode)
	//	if err != nil {
	//		if errors.Is(err, repository2.ErrUserNotFound) {
	//			return nil, fmt.Errorf("invalid invite code")
	//		}
	//		return nil, fmt.Errorf("failed to get inviter: %v", err)
	//	}
	//	inviterID = inviter.CreatorKSUID
	//}

	log.Info().
		Str("email", req.Email).
		Str("ip", req.Ip).
		Msg("开始验证邮箱验证码")

	// 验证邮箱验证码
	//cacheKey := fmt.Sprintf("reg@%s#%s", req.Ip, req.Email)
	//code, err := s.userRepo.GetRegisterCodeWithKey(ctx, cacheKey)
	//if err != nil {
	//	log.Error().
	//		Err(err).
	//		Str("email", req.Email).
	//		Str("cache_key", cacheKey).
	//		Msg("获取注册验证码失败")
	//	return nil, fmt.Errorf("failed to get register code: %v", err)
	//}
	//
	//if code != req.Code {
	//	log.Warn().
	//		Str("email", req.Email).
	//		Str("expected_code", code).
	//		Str("provided_code", req.Code).
	//		Msg("验证码不匹配")
	//	return nil, fmt.Errorf("invalid code")
	//}

	log.Info().
		Str("email", req.Email).
		Msg("验证码验证通过，开始加密密码")

	// 加密密码
	hashedPassword, err := password.Hash(req.Password)
	if err != nil {
		log.Error().
			Err(err).
			Str("email", req.Email).
			Msg("密码加密失败")
		return nil, fmt.Errorf("failed to hash password: %v", err)
	}

	log.Info().
		Str("email", req.Email).
		Msg("密码加密成功，开始确定用户区域")

	// 确定用户区域 (简化的区域检测)
	regionVal := model.RegionGlobal // 默认为全球区域
	if strings.Contains(req.Domain, ".cn") || strings.Contains(req.Ip, "192.168.") {
		regionVal = model.RegionCN
	}

	log.Info().
		Str("email", req.Email).
		Str("domain", req.Domain).
		Str("ip", req.Ip).
		Str("detected_region", regionVal).
		Msg("用户区域检测完成")

	log.Info().
		Str("email", req.Email).
		Str("nickname", req.Nickname).
		Str("region", regionVal).
		Msg("开始创建用户对象")

	// 创建用户
	user := model.NewUser(req.Email, hashedPassword, req.Nickname, regionVal, inviterID)
	user.RegisterIP = req.Ip

	log.Info().
		Str("user_ksuid", user.UserKSUID).
		Str("email", user.Email).
		Str("nickname", user.Nickname).
		Str("region", user.Region).
		Str("register_ip", user.RegisterIP).
		Msg("用户对象创建完成，开始保存到数据库")

	// 保存用户到数据库
	err = s.userRepo.Create(ctx, user)
	if err != nil {
		log.Error().
			Err(err).
			Str("email", req.Email).
			Str("user_ksuid", user.UserKSUID).
			Msg("保存用户到数据库失败")
		return nil, err
	}

	log.Info().
		Str("user_ksuid", user.UserKSUID).
		Str("email", user.Email).
		Str("region", user.Region).
		Msg("用户保存到数据库成功")

	// 发送用户创建事件
	if s.eventPublisher != nil {
		log.Info().
			Str("user_ksuid", user.UserKSUID).
			Msg("开始发送用户注册事件")

		// 发送用户注册事件（用于积分服务和钱包服务）
		if err := s.eventPublisher.PublishUserRegisteredEvent(ctx, user.UserKSUID, user.Email, user.Region); err != nil {
			log.Error().
				Err(err).
				Str("user_ksuid", user.UserKSUID).
				Msg("发送用户注册事件失败")
		} else {
			log.Info().
				Str("user_ksuid", user.UserKSUID).
				Msg("发送用户注册事件成功")
		}
	} else {
		log.Warn().
			Str("user_ksuid", user.UserKSUID).
			Msg("事件发布器未初始化，跳过事件发送")
	}

	// 为用户创建默认收藏夹
	if s.createDefaultFolderFunc != nil {
		log.Info().
			Str("user_ksuid", user.UserKSUID).
			Msg("开始为用户创建默认收藏夹")

		err := s.createDefaultFolderFunc(ctx, user.UserKSUID)
		if err != nil {
			log.Error().
				Err(err).
				Str("user_ksuid", user.UserKSUID).
				Msg("创建默认收藏夹失败")
			// 这里不返回错误，因为用户注册已经成功，收藏夹创建失败不应该影响注册流程
		} else {
			log.Info().
				Str("user_ksuid", user.UserKSUID).
				Msg("创建默认收藏夹成功")
		}
	} else {
		log.Warn().
			Str("user_ksuid", user.UserKSUID).
			Msg("创建默认收藏夹回调函数未设置，跳过默认收藏夹创建")
	}

	log.Info().
		Str("user_ksuid", user.UserKSUID).
		Msg("开始生成JWT令牌")

	// 生成JWT令牌
	token, err := s.generateToken(user)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", user.UserKSUID).
			Msg("生成JWT令牌失败")
		return nil, fmt.Errorf("failed to generate token: %v", err)
	}

	log.Info().
		Str("user_ksuid", user.UserKSUID).
		Str("email", user.Email).
		Str("region", user.Region).
		Msg("用户注册完成，返回响应")

	return &dto.RegisterResponse{
		Token:  token,
		Region: user.Region,
	}, nil
}

// RegisterForTest 测试环境专用注册（跳过邮箱验证）
func (s *UserService) RegisterForTest(ctx context.Context, req *dto.RegisterRequest) (*dto.RegisterTestResponse, error) {
	// 检查邮箱是否已存在
	exists, err := s.userRepo.IsEmailExists(req.Email)
	if err != nil {
		return nil, fmt.Errorf("failed to check email: %v", err)
	}
	if exists {
		return nil, fmt.Errorf("email already exists")
	}

	// 加密密码
	hashedPassword, err := password.Hash(req.Password)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %v", err)
	}

	// 确定用户区域 (简化的区域检测)
	regionVal := model.RegionGlobal // 默认为全球区域
	if strings.Contains(req.Domain, ".cn") || strings.Contains(req.Ip, "192.168.") {
		regionVal = model.RegionCN
	}

	// 创建用户
	user := model.NewUser(req.Email, hashedPassword, req.Nickname, regionVal, "")
	user.RegisterIP = req.Ip

	// 保存用户到数据库
	err = s.userRepo.Create(ctx, user)
	if err != nil {
		log.Error().Err(err).Str("email", req.Email).Msg("Failed to create user")
		return nil, err
	}

	log.Info().
		Str("user_id", user.UserKSUID).
		Str("email", user.Email).
		Str("region", user.Region).
		Msg("Test user registered successfully")

	// 发送用户注册事件
	if s.eventPublisher != nil {
		// 发送用户注册事件（用于积分服务和钱包服务）
		if err := s.eventPublisher.PublishUserRegisteredEvent(ctx, user.UserKSUID, user.Email, user.Region); err != nil {
			log.Error().Err(err).Str("user_id", user.UserKSUID).Msg("发送用户注册事件失败")
		}
	}

	// 为用户创建默认收藏夹
	if s.createDefaultFolderFunc != nil {
		log.Info().
			Str("user_ksuid", user.UserKSUID).
			Msg("开始为测试用户创建默认收藏夹")

		err := s.createDefaultFolderFunc(ctx, user.UserKSUID)
		if err != nil {
			log.Error().
				Err(err).
				Str("user_ksuid", user.UserKSUID).
				Msg("创建默认收藏夹失败")
			// 这里不返回错误，因为用户注册已经成功，收藏夹创建失败不应该影响注册流程
		} else {
			log.Info().
				Str("user_ksuid", user.UserKSUID).
				Msg("创建默认收藏夹成功")
		}
	} else {
		log.Warn().
			Str("user_ksuid", user.UserKSUID).
			Msg("创建默认收藏夹回调函数未设置，跳过默认收藏夹创建")
	}

	// 生成JWT令牌
	token, err := s.generateToken(user)
	if err != nil {
		log.Error().Err(err).Str("user_id", user.UserKSUID).Msg("Failed to generate token")
		return nil, fmt.Errorf("failed to generate token: %v", err)
	}

	return &dto.RegisterTestResponse{
		Token:  token,
		Region: user.Region,
		User: dto.UserInfo{
			UserKSUID: user.UserKSUID,
			Email:     user.Email,
			Username:  user.Username,
			Region:    user.Region,
		},
	}, nil
}

// Login 用户登录
func (s *UserService) Login(ctx context.Context, req *dto.LoginRequest) (*dto.LoginResponseDTO, error) {
	// 获取trace信息
	traceID, spanID := utils.GetTraceInfoFromContext(ctx)

	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("email", req.Email).
		Str("domain", req.Domain).
		Str("ip", req.Ip).
		Msg("开始处理用户登录服务请求")

	// 根据邮箱获取用户
	user, err := s.userRepo.GetUserByEmail(req.Email)
	if err != nil {
		if errors.Is(err, repository2.ErrUserNotFound) {
			log.Warn().
				Str("trace_id", traceID).
				Str("span_id", spanID).
				Str("email", req.Email).
				Str("ip", req.Ip).
				Msg("登录失败：用户不存在")
			return nil, fmt.Errorf("user not found: %w", err)
		}
		log.Error().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Err(err).
			Str("email", req.Email).
			Str("ip", req.Ip).
			Msg("获取用户信息失败")
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("email", req.Email).
		Str("user_ksuid", user.UserKSUID).
		Str("user_region", user.Region).
		Msg("用户信息获取成功，开始验证密码")

	// 验证密码
	if !password.Verify(req.Password, user.Password) {
		log.Warn().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Str("email", req.Email).
			Str("user_ksuid", user.UserKSUID).
			Str("ip", req.Ip).
			Msg("登录失败：密码错误")
		return nil, fmt.Errorf("invalid password")
	}

	log.Info().
		Str("email", req.Email).
		Str("user_ksuid", user.UserKSUID).
		Msg("密码验证通过，开始更新登录信息")

	// 更新用户登录信息
	err = s.userRepo.UpdateUserLoginInfo(user.UserKSUID, req.Ip)
	if err != nil {
		log.Error().
			Err(err).
			Str("email", req.Email).
			Str("user_ksuid", user.UserKSUID).
			Str("ip", req.Ip).
			Msg("更新用户登录信息失败")
		return nil, fmt.Errorf("failed to update login info: %w", err)
	}

	log.Info().
		Str("email", req.Email).
		Str("user_ksuid", user.UserKSUID).
		Msg("登录信息更新成功，开始生成JWT令牌")

	// 生成JWT令牌
	token, err := s.generateToken(user)
	if err != nil {
		log.Error().
			Err(err).
			Str("email", req.Email).
			Str("user_ksuid", user.UserKSUID).
			Msg("生成JWT令牌失败")
		return nil, fmt.Errorf("failed to generate token: %w", err)
	}

	log.Info().
		Str("email", req.Email).
		Str("user_ksuid", user.UserKSUID).
		Str("region", user.Region).
		Str("ip", req.Ip).
		Msg("用户登录成功")

	// 创建响应
	return &dto.LoginResponseDTO{
		Token:  token,
		Region: user.Region,
	}, nil
}

// GetUser 获取用户信息
func (s *UserService) GetUser(ctx context.Context, req *dto.GetUserRequest) (*dto.GetUserResponse, error) {
	log.Info().
		Str("user_ksuid", req.UserKSUID).
		Msg("开始处理获取用户信息服务请求")

	// 根据ID获取用户
	user, err := s.userRepo.GetUserByKSUID(req.UserKSUID)
	if err != nil {
		if errors.Is(err, repository2.ErrUserNotFound) {
			log.Warn().
				Str("user_ksuid", req.UserKSUID).
				Msg("用户不存在")
			return nil, fmt.Errorf("user not found: %w", err) // 用 fmt.Errorf 包装错误
		}
		log.Error().
			Err(err).
			Str("user_ksuid", req.UserKSUID).
			Msg("获取用户信息失败")
		return nil, fmt.Errorf("failed to get user: %w", err) // 用 fmt.Errorf 包装错误
	}

	log.Info().
		Str("user_ksuid", req.UserKSUID).
		Str("email", user.Email).
		Str("nickname", user.Nickname).
		Str("region", user.Region).
		Msg("获取用户信息成功")

	// 创建响应
	return &dto.GetUserResponse{
		User: ToUserDTO(user),
	}, nil
}

// UpdateUser 更新用户信息
func (s *UserService) UpdateUser(ctx context.Context, req *dto.UpdateProfileRequest) (*dto.UpdateUserResponse, error) {
	// 根据ID获取用户
	user, err := s.userRepo.GetUserByKSUID(req.KSUID)
	if err != nil {
		if errors.Is(err, repository2.ErrUserNotFound) {
			return nil, status.Errorf(codes.NotFound, "user not found")
		}
		return nil, status.Errorf(codes.Internal, "failed to get user: %v", err)
	}

	// 更新用户信息
	user.Username = req.Username
	user.Nickname = req.Nickname
	user.Bio = req.Bio
	user.Birthday = req.Birthday
	user.Gender = req.Gender

	// 保存更新
	err = s.userRepo.Update(ctx, user)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to update user: %v", err)
	}

	// 创建响应
	return &dto.UpdateUserResponse{
		User: ToUserDTO(user),
	}, nil
}

func toUserSearchResult(user *model.User) dto.UserSearchResult {
	return dto.UserSearchResult{
		UserKSUID: user.UserKSUID,
		Username:  user.Username,
		Nickname:  user.Nickname,
		UserType:  user.UserType,
		Bio:       user.Bio,
	}
}

// ToUserDTO 将用户模型转换为DTO
func ToUserDTO(user *model.User) *dto.UserDTO {
	return &dto.UserDTO{
		ID:             user.ID,
		UserKSUID:      user.UserKSUID,
		Email:          user.Email,
		Username:       user.Username,
		Nickname:       user.Nickname,
		AvatarURL:      user.AvatarURL,
		FollowersCount: int(user.FollowersCount),
		FollowingCount: user.FollowingCount,
		LikeReceived:   user.LikeReceived,
		IsVerified:     user.IsVerified,
		Gender:         user.Gender,
		Bio:            user.Bio,
		Birthday:       user.Birthday,
		Region:         user.Region,
		CreatedAt:      user.CreatedAt,
		UpdatedAt:      user.UpdatedAt,
		// 隐私设置字段
		HideComment:    user.HideComment,
		SecretLike:     user.SecretLike,
		SecretFavorite: user.SecretFavorite,
	}
}

// DetectRegion 检测用户区域
func (s *UserService) DetectRegion(ctx context.Context, req *dto.DetectRegionRequest) (*dto.DetectRegionResponse, error) {
	// 检测用户区域 (简化的区域检测)
	region := model.RegionGlobal // 默认为全球区域
	if strings.Contains(req.Domain, ".cn") || strings.Contains(req.Ip, "192.168.") {
		region = model.RegionCN
	}

	return &dto.DetectRegionResponse{
		Region: region,
	}, nil
}

// ValidateToken 验证JWT令牌
func (s *UserService) ValidateToken(ctx context.Context, req *dto.ValidateTokenRequest) (*dto.ValidateTokenResponse, error) {
	log.Info().
		Str("token_prefix", req.Token[:min(len(req.Token), 10)]+"...").
		Msg("开始处理JWT令牌验证服务请求")

	claims, err := s.verifyToken(req.Token)
	if err != nil {
		log.Warn().
			Err(err).
			Str("token_prefix", req.Token[:min(len(req.Token), 10)]+"...").
			Msg("JWT令牌验证失败")

		return &dto.ValidateTokenResponse{
			Valid: false,
		}, nil
	}

	log.Info().
		Str("token_prefix", req.Token[:min(len(req.Token), 10)]+"...").
		Msg("JWT令牌解析成功，开始提取用户信息")

	ksUID, ok := claims["user_ksuid"].(string)
	if !ok {
		log.Warn().
			Str("token_prefix", req.Token[:min(len(req.Token), 10)]+"...").
			Interface("claims", claims).
			Msg("JWT令牌中缺少user_ksuid字段")

		return &dto.ValidateTokenResponse{
			Valid: false,
		}, nil
	}

	log.Info().
		Str("user_ksuid", ksUID).
		Str("token_prefix", req.Token[:min(len(req.Token), 10)]+"...").
		Msg("JWT令牌验证成功")

	return &dto.ValidateTokenResponse{
		UserKSUID: ksUID,
		Valid:     true,
	}, nil
}

// min 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// generateToken 生成JWT令牌
func (s *UserService) generateToken(user *model.User) (string, error) {
	// 设置过期时间
	expirationTime := time.Now().Add(s.jwtExpireTime)

	// 创建JWT声明
	claims := jwt.MapClaims{
		"id":          user.ID,
		"user_ksuid":  user.UserKSUID,
		"email":       user.Email,
		"region_code": user.Region,
		"user_level":  user.Level,
		"exp":         expirationTime.Unix(),
	}

	// 添加调试日志
	log.Info().Interface("claims", claims).Msg("生成JWT token的claims内容")

	// 创建令牌
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(s.jwtSecret))
	if err != nil {
		return "", err
	}

	log.Info().Str("token", tokenString).Msg("生成的JWT token")
	return tokenString, nil
}

// verifyToken 验证JWT令牌
func (s *UserService) verifyToken(tokenString string) (jwt.MapClaims, error) {
	// 解析令牌
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		// 验证签名方法
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errors.New("unexpected signing method")
		}
		return []byte(s.jwtSecret), nil
	})
	if err != nil {
		return nil, err
	}

	// 验证令牌
	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, errors.New("invalid token")
}

// GetUserRegion 获取用户所在区域
func (s *UserService) GetUserRegion(ctx context.Context, userKSUID string) (string, error) {
	user, err := s.userRepo.GetByUserKSUID(ctx, userKSUID)
	if err != nil {
		if errors.Is(err, repository2.ErrUserNotFound) {
			return "", status.Errorf(codes.NotFound, "user not found")
		}
		return "", fmt.Errorf("failed to get user: %w", err)
	}

	return user.Region, nil
}

// RequestPasswordReset 请求重置密码
func (s *UserService) RequestPasswordReset(ctx context.Context, email string) error {
	// 检查用户是否存在
	//user, err := s.userRepo.GetUserByEmail(email)
	//if err != nil {
	//	if errors.Is(err, repository2.ErrUserNotFound) {
	//		// 不要透露用户是否存在
	//		log.Info().Str("email", email).Msg("Password reset requested for non-existent user")
	//		return nil
	//	}
	//	return fmt.Errorf("failed to get user: %w", err)
	//}
	//
	//// 生成密码重置令牌
	//resetToken, err := s.authManager.GenerateResetToken()
	//if err != nil {
	//	return fmt.Errorf("failed to generate reset token: %w", err)
	//}
	//
	//// 创建密码重置记录
	//passwordReset := model.NewPasswordReset(
	//	user.CreatorKSUID,
	//	user.Email,
	//	resetToken,
	//	s.config.Mail.ResetTokenExpiration,
	//)
	//
	//// 保存密码重置记录
	//if err := s.userRepo.SavePasswordResetToken(passwordReset); err != nil {
	//	return fmt.Errorf("failed to save reset token: %w", err)
	//}
	//
	//// 创建邮件客户端
	//mailClient := mail.NewMailtrapClient(
	//	s.config.Mail.APIToken,
	//	s.config.Mail.APIEndpoint,
	//	s.config.Mail.FromEmail,
	//	s.config.Mail.FromName,
	//)
	//
	//// 发送密码重置邮件
	//if err := mailClient.SendResetPasswordEmail(user.Email, resetToken); err != nil {
	//	log.Error().Err(err).Str("email", user.Email).Msg("Failed to send password reset email")
	//	return fmt.Errorf("failed to send password reset email: %w", err)
	//}
	//
	//log.Info().Str("user_id", user.CreatorKSUID).Str("email", email).Msg("Password reset token sent")
	//return nil
	return nil
}

// ResetPassword 重置密码
// TODO 重置密码，待完成
func (s *UserService) ResetPassword(ctx context.Context, token, newPassword string) error {
	return nil
}

// ChangePassword 修改用户密码
func (s *UserService) ChangePassword(ctx context.Context, userKSUID, oldPassword, newPassword string) error {
	// 获取用户信息
	user, err := s.userRepo.GetByUserKSUID(ctx, userKSUID)
	if err != nil {
		if errors.Is(err, repository2.ErrUserNotFound) {
			return status.Errorf(codes.NotFound, "user not found")
		}
		return status.Errorf(codes.Internal, "failed to get user: %v", err)
	}

	// 验证旧密码
	if !password.Verify(oldPassword, user.Password) {
		return status.Errorf(codes.Unauthenticated, "invalid old password")
	}

	// 生成新密码的哈希值
	hashedPassword, err := password.Hash(newPassword)
	if err != nil {
		return status.Errorf(codes.Internal, "failed to hash new password: %v", err)
	}

	// 更新用户密码
	user.Password = hashedPassword
	if err := s.userRepo.UpdateUser(user); err != nil {
		return status.Errorf(codes.Internal, "failed to update password: %v", err)
	}

	return nil
}

// SearchUsers 搜索用户
func (s *UserService) SearchUsers(ctx context.Context, req *dto.SearchUsersRequest) (*dto.SearchUsersResponse, error) {
	log.Info().
		Str("query", req.Query).
		Int("page", req.Page).
		Int("page_size", req.PageSize).
		Msg("开始处理搜索用户服务请求")

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 50 {
		req.PageSize = 10
	}

	log.Info().
		Str("query", req.Query).
		Int("page", req.Page).
		Int("page_size", req.PageSize).
		Msg("搜索参数准备完成，开始调用仓库层")

	// 调用仓库层进行搜索
	users, total, err := s.userRepo.SearchUsers(ctx, req.Query, req.Page, req.PageSize)
	if err != nil {
		log.Error().
			Err(err).
			Str("query", req.Query).
			Int("page", req.Page).
			Int("page_size", req.PageSize).
			Msg("仓库层搜索用户失败")
		return nil, fmt.Errorf("failed to search users: %v", err)
	}

	log.Info().
		Str("query", req.Query).
		Int("result_count", len(users)).
		Int64("total", total).
		Msg("仓库层搜索完成，开始转换为DTO")

	// 转换为DTO
	userDTOs := make([]dto.UserSearchResult, len(users))
	for i, user := range users {
		userDTOs[i] = toUserSearchResult(user)
	}

	log.Info().
		Str("query", req.Query).
		Int("result_count", len(userDTOs)).
		Int64("total", total).
		Msg("搜索用户服务请求处理完成")

	return &dto.SearchUsersResponse{
		Users: userDTOs,
		Total: int(total),
	}, nil
}

// isValidUserType 验证用户类型是否有效
func isValidUserType(userType string) bool {
	return model.IsValidUserType(userType)
}

// ListUsers 获取用户列表
func (s *UserService) ListUsers(ctx context.Context, page, pageSize int) ([]*dto.UserDTO, int64, error) {
	users, total, err := s.userRepo.ListUsers(page, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list users: %v", err)
	}

	// 转换为DTO
	userDTOs := make([]*dto.UserDTO, len(users))
	for i, user := range users {
		userDTOs[i] = ToUserDTO(user)
	}

	return userDTOs, total, nil
}

// ListUsersByRegion 按区域获取用户列表
func (s *UserService) ListUsersByRegion(ctx context.Context, region string, page, pageSize int) ([]*dto.UserDTO, int64, error) {
	users, total, err := s.userRepo.ListUsersByRegion(region, page, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list users by region: %v", err)
	}

	// 转换为DTO
	userDTOs := make([]*dto.UserDTO, len(users))
	for i, user := range users {
		userDTOs[i] = ToUserDTO(user)
	}

	return userDTOs, total, nil
}

// ============= 能量系统相关方法 =============

// GetUserEnergy 获取用户能量状态
func (s *UserService) GetUserEnergy(ctx context.Context, userKSUID string) (*model.EnergyResponse, error) {
	user, err := s.userRepo.GetByUserKSUID(ctx, userKSUID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %v", err)
	}

	// 更新能量（计算恢复）
	user.UpdateUserEnergy()

	// 更新到数据库
	if err := s.userRepo.Update(ctx, user); err != nil {
		log.Error().Err(err).Str("user_id", userKSUID).Msg("Failed to update user energy")
	}

	stats := user.GetEnergyStats()
	return &model.EnergyResponse{
		CurrentEnergy:    stats["current_energy"].(float64),
		MaxEnergy:        stats["max_energy"].(float64),
		EnergyRegenRate:  stats["energy_regen_rate"].(float64),
		MinutesToNext:    stats["minutes_to_next"].(float64),
		DailyActionCount: stats["daily_action_count"].(int),
		DailyActionLimit: stats["daily_action_limit"].(int),
		IsMaxed:          stats["is_max"].(bool),
		HasReachedLimit:  stats["has_reached_limit"].(bool),
	}, nil
}

// ConsumeUserEnergy 消耗用户能量
func (s *UserService) ConsumeUserEnergy(ctx context.Context, req *model.ConsumeEnergyRequest) (*model.ConsumeEnergyResponse, error) {
	user, err := s.userRepo.GetByUserKSUID(ctx, req.UserKSUID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %v", err)
	}

	// 消耗能量，返回是否成功、剩余能量和消耗的能量
	success, remainingEnergy, consumedAmount := user.ConsumeEnergy(req.Action)

	// 更新到数据库
	if err := s.userRepo.Update(ctx, user); err != nil {
		return nil, fmt.Errorf("failed to update user: %v", err)
	}

	// 获取最新的能量状态
	energyStats := user.GetEnergyStats()
	energyResponse := model.EnergyResponse{
		CurrentEnergy:    energyStats["current_energy"].(float64),
		MaxEnergy:        energyStats["max_energy"].(float64),
		EnergyRegenRate:  energyStats["energy_regen_rate"].(float64),
		MinutesToNext:    energyStats["minutes_to_next"].(float64),
		DailyActionCount: energyStats["daily_action_count"].(int),
		DailyActionLimit: energyStats["daily_action_limit"].(int),
		IsMaxed:          energyStats["is_max"].(bool),
		HasReachedLimit:  energyStats["has_reached_limit"].(bool),
	}

	return &model.ConsumeEnergyResponse{
		Success:         success,
		RemainingEnergy: remainingEnergy,
		Consumed:        consumedAmount,
		Energy:          energyResponse,
	}, nil
}

// ResetUserEnergy 重置用户能量
func (s *UserService) ResetUserEnergy(ctx context.Context, userID string) error {
	user, err := s.userRepo.GetByUserKSUID(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get user: %v", err)
	}

	user.ResetEnergy()

	if err := s.userRepo.Update(ctx, user); err != nil {
		return fmt.Errorf("failed to update user: %v", err)
	}

	return nil
}

// ============= 奖励系统相关方法 =============

// GetUserRewardStats 获取用户奖励统计
func (s *UserService) GetUserRewardStats(ctx context.Context, userID string) (*model.RewardRateResponse, error) {
	user, err := s.userRepo.GetByUserKSUID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %v", err)
	}

	stats := user.GetRewardStats()
	example := user.RewardCalculationExample()

	return &model.RewardRateResponse{
		PersonalLevel:     stats["personal_level"].(int),
		LevelName:         stats["level_name"].(string),
		LevelDisplayName:  stats["level_display_name"].(string),
		CurrentMultiplier: stats["current_multiplier"].(float64),
		MultiplierPercent: stats["reward_rate_percent"].(int),
		MinRewardRate:     stats["min_reward_rate"].(float64),
		MaxRewardRate:     stats["max_reward_rate"].(float64),
		RewardExample:     example,
	}, nil
}

// AdjustUserReward 调整用户奖励倍率
func (s *UserService) AdjustUserReward(ctx context.Context, req *model.AdjustRewardRequest) (*model.AdjustRewardResponse, error) {
	user, err := s.userRepo.GetByUserKSUID(ctx, req.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %v", err)
	}

	oldMultiplier := user.RewardMultiplier
	newMultiplier := user.AdjustRewardMultiplier(req.Change)

	// 更新到数据库
	if err := s.userRepo.Update(ctx, user); err != nil {
		return nil, fmt.Errorf("failed to update user: %v", err)
	}

	return &model.AdjustRewardResponse{
		Success:        true,
		OldMultiplier:  oldMultiplier,
		NewMultiplier:  newMultiplier,
		Change:         req.Change,
		LimitedByRange: newMultiplier != (oldMultiplier + req.Change),
	}, nil
}

// UpgradeUserPersonalLevel 升级用户个人等级
func (s *UserService) UpgradeUserPersonalLevel(ctx context.Context, req *model.UpgradePersonalLevelRequest) (*model.UpgradePersonalLevelResponse, error) {
	user, err := s.userRepo.GetByUserKSUID(ctx, req.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %v", err)
	}

	oldLevel := user.PersonalLevel
	success := user.UpgradePersonalLevel(req.NewLevel)

	if !success {
		return &model.UpgradePersonalLevelResponse{
			Success: false,
		}, nil
	}

	// 更新到数据库
	if err := s.userRepo.Update(ctx, user); err != nil {
		return nil, fmt.Errorf("failed to update user: %v", err)
	}

	rewardRange := model.GetPersonalLevelRewardRange(req.NewLevel)

	return &model.UpgradePersonalLevelResponse{
		Success:      true,
		OldLevel:     oldLevel,
		NewLevel:     req.NewLevel,
		NewLevelName: rewardRange.DisplayName,
	}, nil
}

// ============= 身份管理相关方法 =============

// ClaimIdolAccount 认领偶像账户
func (s *UserService) ClaimIdolAccount(ctx context.Context, req *model.ClaimAccountRequest) (*model.ClaimAccountResponse, error) {
	user, err := s.userRepo.GetByUserKSUID(ctx, req.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %v", err)
	}

	if !user.CanBeClaimed() {
		return &model.ClaimAccountResponse{
			Success: false,
			Message: "该账户不可认领",
			UserID:  req.UserID,
		}, nil
	}

	user.ClaimAccount(req.ContactInfo)

	// 更新到数据库
	if err := s.userRepo.Update(ctx, user); err != nil {
		return nil, fmt.Errorf("failed to update user: %v", err)
	}

	return &model.ClaimAccountResponse{
		Success: true,
		Message: "账户认领成功",
		UserID:  req.UserID,
	}, nil
}

// GetIdolStats 获取偶像统计信息
func (s *UserService) GetIdolStats(ctx context.Context, userID string) (*model.IdolStatsResponse, error) {
	user, err := s.userRepo.GetByUserKSUID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %v", err)
	}

	stats := user.GetIdolStats()

	return &model.IdolStatsResponse{
		UserID:          user.UserKSUID,
		Name:            user.Nickname,
		UserType:        user.UserType,
		UserTypeDisplay: model.GetUserTypeDisplayName(user.UserType),
		Stats:           stats,
	}, nil
}

// GetUsersByType 根据身份类型获取用户
func (s *UserService) GetUsersByType(ctx context.Context, userType string, page, pageSize int) ([]*dto.UserDTO, int64, error) {
	// 验证用户类型是否有效
	if !model.IsValidUserType(userType) {
		return nil, 0, fmt.Errorf("invalid user type: %s", userType)
	}

	// 调用repository获取用户列表
	users, total, err := s.userRepo.GetUsersByType(ctx, userType, page, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get users by type: %v", err)
	}

	// 转换为DTO
	userDTOs := make([]*dto.UserDTO, len(users))
	for i, user := range users {
		userDTOs[i] = ToUserDTO(user)
	}

	return userDTOs, total, nil
}

// GetUsersByKSUIDs 根据多个KSUID批量获取用户基本信息
func (s *UserService) GetUsersByKSUIDs(ctx context.Context, req *dto.GetUsersByKSUIDsRequest) (*dto.GetUsersByKSUIDsResponse, error) {
	// 验证请求参数
	if len(req.UserKSUIDs) == 0 {
		return &dto.GetUsersByKSUIDsResponse{
			Users: []dto.UserBasicInfo{},
			Total: 0,
		}, nil
	}

	// 调用repository获取用户列表
	users, err := s.userRepo.GetUsersByKSUIDs(ctx, req.UserKSUIDs)

	if err != nil {
		return nil, fmt.Errorf("failed to get users by KSUIDs: %v", err)
	}

	// 转换为基本信息DTO
	userBasicInfos := make([]dto.UserBasicInfo, len(users))
	for i, user := range users {
		userBasicInfos[i] = dto.UserBasicInfo{
			UserKSUID:   user.UserKSUID,
			Username:    user.Username,
			Nickname:    user.Nickname,
			Bio:         user.Bio,
			Avatar:      user.AvatarURL,
			HideComment: user.HideComment,
		}
	}

	return &dto.GetUsersByKSUIDsResponse{
		Users: userBasicInfos,
		Total: len(userBasicInfos),
	}, nil
}

// ============= 等级管理相关方法 =============

// UpgradeUserLevel 升级用户等级
func (s *UserService) UpgradeUserLevel(ctx context.Context, userID string, newLevel int, reason string) (interface{}, error) {
	user, err := s.userRepo.GetByUserKSUID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %v", err)
	}

	oldLevel := user.Level
	user.Level = newLevel

	// 更新能量和奖励系统
	user.UpdateEnergyAndRates()

	// 更新到数据库
	if err := s.userRepo.Update(ctx, user); err != nil {
		return nil, fmt.Errorf("failed to update user: %v", err)
	}

	return map[string]interface{}{
		"success":   true,
		"old_level": oldLevel,
		"new_level": newLevel,
		"reason":    reason,
		"message":   "用户等级升级成功",
	}, nil
}

// GetUserLevelStats 获取用户等级统计
func (s *UserService) GetUserLevelStats(ctx context.Context) (interface{}, error) {
	// 调用repository获取等级统计
	stats, err := s.userRepo.GetUserLevelStats(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get user level stats: %v", err)
	}

	return stats, nil
}

// ============= 批量用户创建相关方法 =============

// BatchCreateUsers 批量创建用户（内部服务使用）
func (s *UserService) BatchCreateUsers(ctx context.Context, req *dto.InternalBatchCreateUserRequest) (*dto.InternalBatchCreateUserResponse, error) {
	log.Info().
		Int("user_count", len(req.Users)).
		Msg("开始批量创建用户")

	response := &dto.InternalBatchCreateUserResponse{
		Results: make([]dto.InternalBatchCreateResult, 0, len(req.Users)),
	}

	// 处理每个用户
	for _, userInfo := range req.Users {
		result := s.processSingleUserCreation(ctx, userInfo)
		response.Results = append(response.Results, result)

		if result.Success {
			response.SuccessCount++
		} else {
			response.FailedCount++
		}
	}

	response.Message = fmt.Sprintf("批量创建完成，成功: %d, 失败: %d", response.SuccessCount, response.FailedCount)

	log.Info().
		Int("success_count", response.SuccessCount).
		Int("failed_count", response.FailedCount).
		Msg("批量创建用户完成")

	return response, nil
}

// processSingleUserCreation 处理单个用户的创建
func (s *UserService) processSingleUserCreation(ctx context.Context, userInfo dto.InternalBatchUserInfo) dto.InternalBatchCreateResult {
	result := dto.InternalBatchCreateResult{
		Email: userInfo.Email,
	}

	// 1. 检查用户是否已存在
	existingUser, err := s.userRepo.GetByEmail(ctx, userInfo.Email)
	if err != nil && !errors.Is(err, repository2.ErrUserNotFound) {
		result.Error = fmt.Sprintf("检查用户是否存在失败: %v", err)
		return result
	}

	// 2. 如果用户已存在，检查并更新角色
	if existingUser != nil {
		result.UserKSUID = existingUser.UserKSUID
		result.Action = "skipped"

		// 检查用户是否需要添加新角色
		if len(userInfo.Roles) > 0 {
			updated, updateErr := s.updateExistingUserRoles(ctx, existingUser, userInfo.Roles)
			if updateErr != nil {
				result.Error = fmt.Sprintf("更新用户角色失败: %v", updateErr)
				return result
			}
			if updated {
				result.Action = "updated_roles"
			}
		}

		result.Success = true
		return result
	}

	// 3. 创建新用户
	newUser, createErr := s.createNewUserFromBatchInfo(userInfo)
	if createErr != nil {
		result.Error = fmt.Sprintf("创建用户失败: %v", createErr)
		return result
	}

	// 4. 保存用户到数据库
	if err := s.userRepo.Create(ctx, newUser); err != nil {
		result.Error = fmt.Sprintf("保存用户到数据库失败: %v", err)
		return result
	}

	result.UserKSUID = newUser.UserKSUID
	result.Action = "created"

	// 5. 创建用户角色
	if len(userInfo.Roles) > 0 {
		if roleErr := s.createUserRolesForBatch(ctx, newUser.UserKSUID, userInfo.Roles, userInfo.Region); roleErr != nil {
			result.Error = fmt.Sprintf("创建用户角色失败: %v", roleErr)
			return result
		}
	}

	result.Success = true
	return result
}

// createNewUserFromBatchInfo 从批量信息创建新用户对象
func (s *UserService) createNewUserFromBatchInfo(userInfo dto.InternalBatchUserInfo) (*model.User, error) {
	// 生成随机密码（系统用户不能直接登录）
	randomPassword := fmt.Sprintf("sys_%d_%s", time.Now().Unix(), userInfo.Email[:5])
	hashedPassword, err := password.Hash(randomPassword)
	if err != nil {
		return nil, fmt.Errorf("密码加密失败: %v", err)
	}

	// 如果没有提供用户名，使用昵称生成
	username := userInfo.Username
	if username == "" {
		username = strings.ReplaceAll(userInfo.Nickname, " ", "_")
	}

	user := &model.User{
		UserKSUID:       ksuid.GenerateKSUID(), // 临时使用邮箱，后面会生成KSUID
		Email:           userInfo.Email,
		Username:        username,
		Password:        hashedPassword,
		Nickname:        userInfo.Nickname,
		RealName:        userInfo.RealName,
		UserType:        userInfo.UserType,
		Bio:             userInfo.Bio,
		Region:          userInfo.Region,
		Level:           0,
		Status:          model.UserStatusActive,
		IsSystemCreated: true,  // 标记为系统创建
		IsClaimed:       false, // 默认未认领
		EmailVerified:   false, // 系统用户邮箱不需要验证
	}

	return user, nil
}

// updateExistingUserRoles 更新现有用户的角色
func (s *UserService) updateExistingUserRoles(ctx context.Context, user *model.User, requiredRoles []string) (bool, error) {
	// 获取用户现有角色
	existingRoles, err := s.roleRepo.GetByUserID(ctx, user.UserKSUID)
	if err != nil {
		return false, err
	}

	// 创建现有角色类型的映射
	existingRoleTypes := make(map[string]bool)
	for _, role := range existingRoles {
		existingRoleTypes[role.Type] = true
	}

	// 检查需要添加的角色
	updated := false
	for _, roleType := range requiredRoles {
		if !existingRoleTypes[roleType] {
			// 创建新角色
			if err := s.createSingleUserRole(ctx, user.UserKSUID, roleType, user.Region); err != nil {
				return false, err
			}
			updated = true
		}
	}

	return updated, nil
}

// createUserRolesForBatch 为批量创建的用户创建角色
func (s *UserService) createUserRolesForBatch(ctx context.Context, userKSUID string, roleTypes []string, region string) error {
	for _, roleType := range roleTypes {
		if err := s.createSingleUserRole(ctx, userKSUID, roleType, region); err != nil {
			return err
		}
	}
	return nil
}

// createSingleUserRole 创建单个用户角色
func (s *UserService) createSingleUserRole(ctx context.Context, userKSUID, roleType, region string) error {
	// 验证角色类型
	if !s.isValidRoleTypeForBatch(roleType) {
		return fmt.Errorf("无效的角色类型: %s", roleType)
	}

	// 创建角色
	role := model.NewRole(userKSUID, roleType, model.RoleLevelD, region)

	if err := s.roleRepo.Create(ctx, role); err != nil {
		return fmt.Errorf("创建角色失败: %v", err)
	}

	// 获取角色的默认权限
	permissions := model.GetDefaultPermissionsByRole(roleType, model.RoleLevelD, false)

	// 保存角色权限
	for _, perm := range permissions {
		if err := s.roleRepo.AddPermission(ctx, role.ID, perm); err != nil {
			log.Error().Err(err).
				Str("roleID", fmt.Sprintf("%d", role.ID)).
				Str("permission", perm).
				Msg("添加角色权限失败")
		}
	}

	return nil
}

// isValidRoleTypeForBatch 验证批量创建时的角色类型是否有效
func (s *UserService) isValidRoleTypeForBatch(roleType string) bool {
	validRoles := []string{
		model.RoleTypeCreator,
		model.RoleTypeSharer,
		model.RoleTypeAuditor,
		model.RoleTypeVersatile,
		model.RoleTypeShareholder,
		model.RoleTypeFounder,
	}

	for _, validRole := range validRoles {
		if roleType == validRole {
			return true
		}
	}
	return false
}
