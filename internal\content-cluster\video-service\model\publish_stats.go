package model

import (
	"gorm.io/gorm"
	"time"
)

// PublishStats 发布视频统计表模型
type PublishStats struct {
	ID uint `gorm:"primaryKey" json:"id"`

	// 基础信息
	UserKSUID    string `gorm:"column:user_ksuid;type:varchar(32);not null;unique" json:"user_ksuid"`  // 用户KSUID
	ContentJSON  string `gorm:"column:content_json;type:jsonb;not null" json:"content_json"`           // 内容JSON数据
	MainCategory uint   `gorm:"column:main_category;type:integer;not null;index" json:"main_category"` // 主分类ID
	FollowersCount int64 `gorm:"column:followers_count;type:bigint;default:0;index" json:"followers_count"` // 粉丝数量

	// 基础字段
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
}

// TableName 指定表名
func (*PublishStats) TableName() string {
	return "video_publish_stats"
}
