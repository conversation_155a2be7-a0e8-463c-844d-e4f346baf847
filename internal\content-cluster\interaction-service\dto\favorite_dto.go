package dto

import (
	"time"
)

// ===== 收藏夹相关DTO =====

// CreateFavoriteFolderRequest 创建收藏夹请求
type CreateFavoriteFolderRequest struct {
	DirName     string `json:"dir_name" binding:"required,min=1,max=20" example:"我的收藏"` // 收藏夹名称
	Description string `json:"description" binding:"max=500" example:"这是我的个人收藏夹"`       // 收藏夹描述
	IsPublic    bool   `json:"is_public" example:"false"`                               // 是否公开
}

// UpdateFavoriteFolderRequest 更新收藏夹请求
type UpdateFavoriteFolderRequest struct {
	DirName     string `json:"dir_name" binding:"required,min=1,max=100" example:"我的收藏"` // 收藏夹名称
	Description string `json:"description" binding:"max=500" example:"这是我的个人收藏夹"`        // 收藏夹描述
	IsPublic    bool   `json:"is_public" example:"false"`                                // 是否公开
}

// FavoriteFolderResponse 收藏夹响应
type FavoriteFolderResponse struct {
	FavoriteFolderID string    `json:"favorite_folder_id" example:"01ARZ3NDEKTSV4RRFFQ69G5FAV"` // 收藏夹ID
	UserKSUID        string    `json:"user_ksuid" example:"01ARZ3NDEKTSV4RRFFQ69G5FAV"`         // 用户ID
	DirName          string    `json:"dir_name" example:"我的收藏"`                                 // 收藏夹名称
	Description      string    `json:"description" example:"这是我的个人收藏夹"`                         // 收藏夹描述
	IsPublic         bool      `json:"is_public" example:"false"`                               // 是否公开
	SortOrder        int       `json:"sort_order" example:"0"`                                  // 排序顺序
	IsDefault        bool      `json:"is_default" example:"false"`                              // 是否为默认收藏夹
	ItemCount        int64     `json:"item_count" example:"10"`                                 // 收藏项数量
	CreatedAt        time.Time `json:"created_at"`                                              // 创建时间
	UpdatedAt        time.Time `json:"updated_at"`                                              // 更新时间
}

// GetFavoriteFoldersRequest 获取收藏夹列表请求
type GetFavoriteFoldersRequest struct {
	UserKSUID string `form:"user_ksuid" example:"01ARZ3NDEKTSV4RRFFQ69G5FAV"` // 用户ID
	Page      int    `form:"page" binding:"min=1,max=20" example:"1"`         // 页码
	PageSize  int    `form:"page_size" binding:"min=1,max=20" example:"20"`   // 每页数量
}

// GetFavoriteFoldersResponse 获取收藏夹列表响应
type GetFavoriteFoldersResponse struct {
	Folders    []*FavoriteFolderResponse `json:"folders"`     // 收藏夹列表
	Total      int64                     `json:"total"`       // 总数
	Page       int                       `json:"page"`        // 当前页码
	PageSize   int                       `json:"page_size"`   // 每页数量
	TotalPages int                       `json:"total_pages"` // 总页数
}

// ===== 收藏项相关DTO =====



// ManageFavoriteRequest 管理收藏请求（合并添加和移动功能）
type ManageFavoriteRequest struct {
	ContentKSUID           string   `json:"content_ksuid" binding:"required" example:"01ARZ3NDEKTSV4RRFFQ69G5FAV"`    // 内容ID
	ContentType            string   `json:"content_type" binding:"required" example:"video"`                          // 内容类型
	AddFavoriteFolderIDs   []string `json:"add_favorite_folder_ids,omitempty" example:"[\"01ARZ3NDEKTSV4RRFFQ69G5FAV\"]"` // 需要添加到的收藏夹ID数组
	DelFavoriteFolderIDs   []string `json:"del_favorite_folder_ids,omitempty" example:"[\"01ARZ3NDEKTSV4RRFFQ69G5FAV\"]"` // 需要从中删除的收藏夹ID数组
}

// CheckFavoriteStatusRequest 检查收藏状态请求
type CheckFavoriteStatusRequest struct {
	ContentKSUID string `form:"content_ksuid" binding:"required" example:"01ARZ3NDEKTSV4RRFFQ69G5FAV"` // 内容ID
}

// CheckFavoriteStatusResponse 检查收藏状态响应
type CheckFavoriteStatusResponse struct {
	IsFavorited bool                      `json:"is_favorited" example:"true"` // 是否已收藏
	Folders     []*FavoriteFolderResponse `json:"folders,omitempty"`           // 收藏到的收藏夹列表
	Items       []*FavoriteItemResponse   `json:"items,omitempty"`             // 收藏项详情
}

// ContentInfo 内容信息（从client包导入的结构）
type ContentInfo struct {
	UserKSUID    string  `json:"user_ksuid"`
	ContentKSUID string  `json:"content_ksuid"`
	Title        string  `json:"title"`
	CoverURL     string  `json:"cover_url"`
	Duration     float64 `json:"duration"`
	ViewCount    int64   `json:"view_count"`
	LikeCount    int64   `json:"like_count"`
	DislikeCount int64   `json:"dislike_count"`
	CommentCount int64   `json:"comment_count"`
}

// FavoriteItemResponse 收藏项响应
type FavoriteItemResponse struct {
	FavoriteItemID   string                  `json:"favorite_item_id" example:"01ARZ3NDEKTSV4RRFFQ69G5FAV"`   // 收藏项ID
	UserKSUID        string                  `json:"user_ksuid" example:"01ARZ3NDEKTSV4RRFFQ69G5FAV"`         // 用户ID
	ContentKSUID     string                  `json:"content_ksuid" example:"01ARZ3NDEKTSV4RRFFQ69G5FAV"`      // 内容ID
	ContentType      string                  `json:"content_type" example:"video"`                            // 内容类型
	FavoriteFolderID string                  `json:"favorite_folder_id" example:"01ARZ3NDEKTSV4RRFFQ69G5FAV"` // 收藏夹ID
	FavoriteFolder   *FavoriteFolderResponse `json:"favorite_folder,omitempty"`                               // 收藏夹信息
	ContentInfo      *ContentInfo            `json:"content_info,omitempty"`                                  // 内容详细信息
	CreatedAt        time.Time               `json:"created_at"`                                              // 创建时间
	UpdatedAt        time.Time               `json:"updated_at"`                                              // 更新时间
}

// GetFavoriteItemsRequest 获取收藏项列表请求
type GetFavoriteItemsRequest struct {
	FavoriteFolderID string `form:"favorite_folder_id" binding:"required" example:"01ARZ3NDEKTSV4RRFFQ69G5FAV"` // 收藏夹ID
	ContentType      string `form:"content_type" example:"video"`                                               // 内容类型过滤
	SortBy           string `form:"sort_by" example:"created_at"`                                               // 排序字段
	SortOrder        string `form:"sort_order" example:"desc"`                                                  // 排序方向
	Page             int    `form:"page" binding:"min=1,max=20,required" example:"1"`                           // 页码
	PageSize         int    `form:"page_size" binding:"min=1,max=30,required" example:"20"`                     // 每页数量
}

// GetFavoriteItemsResponse 获取收藏项列表响应
type GetFavoriteItemsResponse struct {
	Items      []*FavoriteItemResponse `json:"items"`       // 收藏项列表
	Total      int64                   `json:"total"`       // 总数
	Page       int                     `json:"page"`        // 当前页码
	PageSize   int                     `json:"page_size"`   // 每页数量
	TotalPages int                     `json:"total_pages"` // 总页数
}

// BatchFavoriteOperationResponse 批量收藏操作响应
type BatchFavoriteOperationResponse struct {
	Success        bool `json:"success" example:"true"`      // 操作是否成功
	TotalRequested int  `json:"total_requested" example:"5"` // 请求处理的总数
	SuccessCount   int  `json:"success_count" example:"3"`   // 成功处理的数量
	SkippedCount   int  `json:"skipped_count" example:"2"`   // 跳过的数量（如重复收藏）
}

// FavoriteStats 收藏统计信息
type FavoriteStats struct {
	TotalFolders  int64 `json:"total_folders" example:"5"`  // 总收藏夹数
	TotalItems    int64 `json:"total_items" example:"100"`  // 总收藏项数
	PublicFolders int64 `json:"public_folders" example:"2"` // 公开收藏夹数
}

// GetFavoriteStatsResponse 获取收藏统计信息响应
type GetFavoriteStatsResponse struct {
	Stats *FavoriteStats `json:"stats"` // 统计信息
}

// ===== 内容收藏统计相关DTO =====

// ContentFavoriteStatsResponse 内容收藏统计响应
type ContentFavoriteStatsResponse struct {
	ContentKSUID  string `json:"content_ksuid" example:"01ARZ3NDEKTSV4RRFFQ69G5FAV"` // 内容ID
	ContentType   string `json:"content_type" example:"video"`                       // 内容类型
	FavoriteCount int64  `json:"favorite_count" example:"1000"`                      // 收藏数量
	CreatedAt     string `json:"created_at"`                                         // 创建时间
	UpdatedAt     string `json:"updated_at"`                                         // 更新时间
}

// GetContentFavoriteStatsRequest 获取内容收藏统计请求
type GetContentFavoriteStatsRequest struct {
	ContentKSUID string `form:"content_ksuid" binding:"required" example:"01ARZ3NDEKTSV4RRFFQ69G5FAV"` // 内容ID
}

// BatchGetContentFavoriteStatsRequest 批量获取内容收藏统计请求
type BatchGetContentFavoriteStatsRequest struct {
	ContentKSUIDs []string `json:"content_ksuids" binding:"required,min=1,max=100" example:"[\"01ARZ3NDEKTSV4RRFFQ69G5FAV\"]"` // 内容ID数组
}

// BatchGetContentFavoriteStatsResponse 批量获取内容收藏统计响应
type BatchGetContentFavoriteStatsResponse struct {
	Stats map[string]*ContentFavoriteStatsResponse `json:"stats"` // 统计信息映射，key为content_ksuid
}

// GetTopFavoritedContentRequest 获取最受欢迎内容请求
type GetTopFavoritedContentRequest struct {
	ContentType string `form:"content_type" example:"video"`               // 内容类型过滤
	Limit       int    `form:"limit" binding:"min=1,max=100" example:"10"` // 限制数量
}

// GetTopFavoritedContentResponse 获取最受欢迎内容响应
type GetTopFavoritedContentResponse struct {
	Contents []*ContentFavoriteStatsResponse `json:"contents"` // 内容列表
}
