# 黑名单API文档

## 概述

本文档描述了PXPAT后端服务的黑名单管理API，基于OpenAPI 3.0规范。

## 文件说明

- `blacklist-api-openapi3.json` - 完整的OpenAPI 3.0规范文档

## API端点

### 1. 拉黑用户
- **POST** `/api/blacklist/block`
- **描述**: 将指定用户添加到黑名单，同时删除双方的关注关系
- **认证**: 需要Bearer Token

### 2. 取消拉黑用户
- **DELETE** `/api/blacklist/block`
- **描述**: 将指定用户从黑名单中移除
- **认证**: 需要Bearer Token

### 3. 获取黑名单列表
- **GET** `/api/blacklist/`
- **描述**: 获取当前用户的黑名单用户列表，支持分页
- **认证**: 需要Bearer Token

### 4. 检查黑名单状态
- **GET** `/api/blacklist/status`
- **描述**: 检查当前用户与目标用户之间的黑名单关系状态
- **认证**: 需要Bearer Token

## 使用方法

### 1. 在线查看
可以将`blacklist-api-openapi3.json`文件导入到以下工具中查看：
- [Swagger Editor](https://editor.swagger.io/)
- [Postman](https://www.postman.com/)
- [Insomnia](https://insomnia.rest/)

### 2. 生成客户端代码
使用OpenAPI Generator生成各种语言的客户端代码：
```bash
# 安装OpenAPI Generator
npm install @openapitools/openapi-generator-cli -g

# 生成JavaScript客户端
openapi-generator-cli generate -i blacklist-api-openapi3.json -g javascript -o ./client/js

# 生成Python客户端
openapi-generator-cli generate -i blacklist-api-openapi3.json -g python -o ./client/python
```

### 3. 本地预览
使用Swagger UI本地预览：
```bash
# 使用Docker运行Swagger UI
docker run -p 8080:8080 -e SWAGGER_JSON=/app/blacklist-api-openapi3.json -v $(pwd):/app swaggerapi/swagger-ui
```

## 认证说明

所有API端点都需要JWT认证，请在请求头中包含：
```
Authorization: Bearer <your-jwt-token>
```

## 错误码说明

- `0`: 成功
- `10000`: 请求参数错误
- `10001`: 无效的认证令牌
- `-1`: 服务器内部错误

## 示例请求

### 拉黑用户
```bash
curl -X POST "https://api.pxpat.com/api/blacklist/block" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "blocked_ksuid": "2SwDAAB2EBtoDWgEHWcBCuMSNDA"
  }'
```

### 获取黑名单列表
```bash
curl -X GET "https://api.pxpat.com/api/blacklist/?page=1&page_size=20" \
  -H "Authorization: Bearer <token>"
```

## 更新日志

- v1.0.0 (2024-01-30): 初始版本，包含基本的黑名单管理功能
