package repository

import (
	"context"
	"errors"
	"pxpat-backend/internal/user-cluster/user-service/dto"
	"pxpat-backend/internal/user-cluster/user-service/model"
)

// 定义黑名单相关错误
var (
	ErrBlacklistNotFound       = errors.New("blacklist relationship not found")
	ErrAlreadyBlacklisted      = errors.New("already blacklisted this user")
	ErrCannotBlacklistSelf     = errors.New("cannot blacklist yourself")
	ErrBlacklistRelationExists = errors.New("blacklist relation already exists")
)

// BlacklistRepository 黑名单仓库接口
type BlacklistRepository interface {
	// CreateBlacklist 创建黑名单关系
	CreateBlacklist(ctx context.Context, blacklist *model.UserBlacklist) error

	// DeleteBlacklist 删除黑名单关系（硬删除）
	DeleteBlacklist(ctx context.Context, blockerKSUID, blockedKSUID string) error

	// GetBlacklist 获取黑名单关系
	GetBlacklist(ctx context.Context, blockerKSUID, blockedKSUID string) (*model.UserBlacklist, error)

	// IsBlacklisted 检查是否已拉黑
	IsBlacklisted(ctx context.Context, blockerKSUID, blockedKSUID string) (bool, error)

	// GetBlacklistUsers 获取用户的黑名单列表
	GetBlacklistUsers(ctx context.Context, userKSUID string, page, pageSize int) ([]*dto.UserBlacklistInfo, int64, error)

	// GetBlacklistCount 获取黑名单数量
	GetBlacklistCount(ctx context.Context, userKSUID string) (int64, error)

	// BatchCheckBlacklistStatus 批量检查黑名单状态
	BatchCheckBlacklistStatus(ctx context.Context, currentUserKSUID string, targetUserKSUIDs []string) ([]dto.UserBlacklistStatus, error)

	// UnblockFollow 取消屏蔽关注关系
	UnblockFollow(ctx context.Context, followerKSUID, followeeKSUID string) error

	// BlockUserAndRemoveFollow 拉黑用户并删除关注关系
	BlockUserAndRemoveFollow(ctx context.Context, blockerKSUID, blockedKSUID string) error
}
