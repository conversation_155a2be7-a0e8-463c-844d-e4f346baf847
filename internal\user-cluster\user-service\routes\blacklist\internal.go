package blacklist

import (
	"github.com/gin-gonic/gin"
	intraHandler "pxpat-backend/internal/user-cluster/user-service/intra/handler"
)

// RegisterBlacklistInternalRoutes 注册黑名单内部服务路由
func RegisterBlacklistInternalRoutes(r *gin.RouterGroup, handler *intraHandler.InternalBlacklistHandler) {
	serviceGroup := r.Group("/intra/blacklist")
	{
		// 微服务内部接口
		serviceGroup.POST("/check-status", handler.CheckBlacklistStatusForService) // 检查黑名单状态
	}
}
