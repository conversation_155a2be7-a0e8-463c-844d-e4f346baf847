package service

import (
	"context"

	"github.com/rs/zerolog/log"
	"pxpat-backend/internal/content-cluster/interaction-service/dto"
	"pxpat-backend/internal/content-cluster/interaction-service/model"
	"pxpat-backend/internal/content-cluster/interaction-service/repository"
)

// InternalLikeService 内部点赞服务实现
type InternalLikeService struct {
	likeRepo repository.LikeRepository
}

// NewInternalLikeService 创建内部点赞服务实例
func NewInternalLikeService(likeRepo repository.LikeRepository) *InternalLikeService {
	return &InternalLikeService{
		likeRepo: likeRepo,
	}
}

// CheckUserLikeStatus 检查用户对内容的点赞状态
func (s *InternalLikeService) CheckUserLikeStatus(userKSUID, contentKSUID string) (likeType string, exists bool, err error) {
	log.Debug().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", contentKSUID).
		Msg("检查用户点赞状态")

	ctx := context.Background()
	likeTypeEnum, exists, err := s.likeRepo.GetUserLikeContentStatus(ctx, userKSUID, contentKSUID)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", contentKSUID).
			Msg("检查用户点赞状态失败")
		return "", false, err
	}

	log.Debug().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", contentKSUID).
		Str("like_type", string(likeTypeEnum)).
		Bool("exists", exists).
		Msg("检查用户点赞状态成功")

	return string(likeTypeEnum), exists, nil
}

// GetContentLikeCounts 获取内容的点赞统计数据
func (s *InternalLikeService) GetContentLikeCounts(contentKSUID string) (likeCount, dislikeCount int64, err error) {
	log.Debug().
		Str("content_ksuid", contentKSUID).
		Msg("获取内容点赞统计")

	ctx := context.Background()
	likeCount, dislikeCount, err = s.likeRepo.GetLikeCounts(ctx, contentKSUID)
	if err != nil {
		log.Error().
			Err(err).
			Str("content_ksuid", contentKSUID).
			Msg("获取内容点赞统计失败")
		return 0, 0, err
	}

	log.Debug().
		Str("content_ksuid", contentKSUID).
		Int64("like_count", likeCount).
		Int64("dislike_count", dislikeCount).
		Msg("获取内容点赞统计成功")

	return likeCount, dislikeCount, nil
}

// BatchGetContentLikeStats 批量获取内容点赞统计
func (s *InternalLikeService) BatchGetContentLikeStats(contentKSUIDs []string) (map[string]dto.ContentLikeStatsItem, error) {
	if len(contentKSUIDs) == 0 {
		return make(map[string]dto.ContentLikeStatsItem), nil
	}

	log.Debug().
		Int("content_count", len(contentKSUIDs)).
		Msg("批量获取内容点赞统计")

	ctx := context.Background()
	statsMap, err := s.likeRepo.GetContentLikeStats(ctx, contentKSUIDs)
	if err != nil {
		log.Error().
			Err(err).
			Int("content_count", len(contentKSUIDs)).
			Msg("批量获取内容点赞统计失败")
		return nil, err
	}

	// 转换为服务层格式
	result := make(map[string]dto.ContentLikeStatsItem)
	for contentKSUID, stats := range statsMap {
		result[contentKSUID] = dto.ContentLikeStatsItem{
			ContentKSUID: stats.ContentKSUID,
			LikeCount:    stats.LikeCount,
			DislikeCount: stats.DislikeCount,
		}
	}

	log.Debug().
		Int("content_count", len(contentKSUIDs)).
		Int("result_count", len(result)).
		Msg("批量获取内容点赞统计成功")

	return result, nil
}

// BatchCheckUserLikeStatus 批量检查用户点赞状态
func (s *InternalLikeService) BatchCheckUserLikeStatus(userKSUID string, contentKSUIDs []string) (map[string]dto.UserLikeStatusItem, error) {
	if len(contentKSUIDs) == 0 {
		return make(map[string]dto.UserLikeStatusItem), nil
	}

	log.Debug().
		Str("user_ksuid", userKSUID).
		Int("content_count", len(contentKSUIDs)).
		Msg("批量检查用户点赞状态")

	ctx := context.Background()
	likeMap, err := s.likeRepo.BatchGetUserLikeStatus(ctx, userKSUID, contentKSUIDs)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Int("content_count", len(contentKSUIDs)).
			Msg("批量检查用户点赞状态失败")
		return nil, err
	}

	// 转换为服务层格式
	result := make(map[string]dto.UserLikeStatusItem)
	for _, contentKSUID := range contentKSUIDs {
		if like, exists := likeMap[contentKSUID]; exists {
			result[contentKSUID] = dto.UserLikeStatusItem{
				Type:       string(like.Type),
				IsLiked:    like.Type == model.LikeTypeLike,
				IsDisliked: like.Type == model.LikeTypeDislike,
			}
		} else {
			result[contentKSUID] = dto.UserLikeStatusItem{
				Type:       "",
				IsLiked:    false,
				IsDisliked: false,
			}
		}
	}

	log.Debug().
		Str("user_ksuid", userKSUID).
		Int("content_count", len(contentKSUIDs)).
		Int("found_count", len(likeMap)).
		Msg("批量检查用户点赞状态成功")

	return result, nil
}
