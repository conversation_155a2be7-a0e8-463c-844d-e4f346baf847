package favorite

import (
	"github.com/gin-gonic/gin"
	"pxpat-backend/internal/content-cluster/interaction-service/external/handler"
	"pxpat-backend/pkg/auth"
	authMiddleware "pxpat-backend/pkg/middleware/auth"
)

// RegisterFavoriteExternalRoutes 注册收藏功能的外部API路由
func RegisterFavoriteExternalRoutes(
	r *gin.RouterGroup,
	favoriteFolderHandler *handler.FavoriteFolderHandler,
	favoriteItemHandler *handler.FavoriteItemHandler,
	jwtManager *auth.Manager,
) {
	favoriteGroup := r.Group("/favorites")

	// 需要认证的路由
	authGroup := favoriteGroup.Group("")
	authGroup.Use(authMiddleware.UserAuthMiddleware(*jwtManager))
	{
		// 收藏夹管理路由
		folders := authGroup.Group("/folders")
		{
			folders.POST("", favoriteFolderHandler.CreateFolder)              // 创建收藏夹
			folders.PUT("/:folder_id", favoriteFolderHandler.UpdateFolder)    // 更新收藏夹
			folders.DELETE("/:folder_id", favoriteFolderHandler.DeleteFolder) // 删除收藏夹
			folders.GET("/:folder_id", favoriteFolderHandler.GetFolder)       // 获取收藏夹详情
			folders.GET("", favoriteFolderHandler.GetFolders)                 // 获取收藏夹列表
		}

		// 收藏项管理路由
		items := authGroup.Group("/items")
		{
			items.PUT("/manage", favoriteItemHandler.ManageFavorite) // 管理收藏（合并添加和删除）
			items.GET("", favoriteItemHandler.GetFavoriteItems)      // 获取收藏项列表
		}

	}

	// 公开路由（不需要认证）
	publicGroup := favoriteGroup.Group("/public")
	{
		publicGroup.GET("/top-favorited", favoriteItemHandler.GetTopFavoritedContent) // 获取最受欢迎的内容
	}
}
