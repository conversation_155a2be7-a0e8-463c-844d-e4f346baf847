package main

import (
	"context"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"go.uber.org/fx"
	"gorm.io/gorm"
	"os"
	"pxpat-backend/cmd"
	"pxpat-backend/internal/user-cluster/user-service/external/handler"
	"pxpat-backend/internal/user-cluster/user-service/external/service"
	intraHandler "pxpat-backend/internal/user-cluster/user-service/intra/handler"
	intraService "pxpat-backend/internal/user-cluster/user-service/intra/service"
	"pxpat-backend/internal/user-cluster/user-service/messaging"
	"pxpat-backend/internal/user-cluster/user-service/migrations"
	"pxpat-backend/internal/user-cluster/user-service/repository"
	"pxpat-backend/internal/user-cluster/user-service/repository/impl"
	"pxpat-backend/internal/user-cluster/user-service/routes"
	"pxpat-backend/internal/user-cluster/user-service/types"
	"pxpat-backend/pkg/auth"
	configLoader "pxpat-backend/pkg/config"
	"pxpat-backend/pkg/consul"
	"pxpat-backend/pkg/consul/health"
	"pxpat-backend/pkg/database"
	pkgMessaging "pxpat-backend/pkg/messaging"
	pkgcors "pxpat-backend/pkg/middleware/cors"
	"pxpat-backend/pkg/region"
)

// 提供配置
func provideConfig() *types.Config {
	clusterName := "user"
	serviceName := "user"

	return configLoader.Load[types.Config](configLoader.LoaderConfig{
		TypeName:    configLoader.Service,
		ClusterName: clusterName,
		ServiceName: serviceName,
		UseRedis:    true,
	})
}

// 提供日志器
func provideLogger() {
	// 配置日志
	zerolog.TimeFieldFormat = zerolog.TimeFormatUnix
	log.Logger = log.Output(zerolog.ConsoleWriter{Out: os.Stderr})

	log.Info().Msg("User service starting...")
}

// 提供数据库连接
func provideDatabase(cfg *types.Config) (*gorm.DB, error) {
	db, err := database.ConnectDB(cfg.Database)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to connect to database")
		return nil, err
	}

	// 注册数据库模型
	err = migrations.Migrates(db)
	if err != nil {
		log.Error().Err(err).Msg("Failed to auto migrate database")
	}

	return db, nil
}

// 提供Redis连接
func provideRedis(cfg *types.Config) (*redis.Client, error) {
	rdb, err := database.ConnectRedis(cfg.Redis)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to connect to redis")
		return nil, err
	}
	return rdb, nil
}

// 提供区域检测器
func provideRegionDetector(cfg *types.Config) region.RegionDetector {
	return region.NewRegionDetector(&cfg.RegionConfig)
}

// 提供Repository层
func provideRepositories(db *gorm.DB, rdb *redis.Client) (
	repository.UserRepository,
	repository.RoleRepository,
	repository.VerificationRepository,
	repository.ExperienceRepository,
	repository.ReputationRepository,
	repository.FollowRepository,
	repository.BlacklistRepository,
) {
	userRepo := impl.NewUserRepository(db, rdb)
	roleRepo := impl.NewRoleRepository(db)
	verificationRepo := impl.NewGormVerificationRepository(db)
	experienceRepo := impl.NewExperienceRepository(db)
	reputationRepo := impl.NewReputationRepository(db)
	followRepo := impl.NewFollowRepository(db)
	blacklistRepo := impl.NewBlacklistRepository(db)

	return userRepo, roleRepo, verificationRepo, experienceRepo, reputationRepo, followRepo, blacklistRepo
}

// 提供Point服务客户端
func providePointServiceClient(cfg *types.Config) (service.PointServiceClient, error) {
	pointServiceConfig := service.PointServiceConfig{
		BaseURL:    cfg.PointService.BaseURL,    // 从配置文件读取
		APIKey:     cfg.PointService.APIKey,     // 从配置文件读取
		Timeout:    cfg.PointService.Timeout,    // 从配置文件读取
		MaxRetries: cfg.PointService.MaxRetries, // 从配置文件读取
	}

	pointClient, err := service.NewPointServiceClient(pointServiceConfig)
	if err != nil {
		log.Warn().Err(err).Msg("Failed to initialize Point service client, point account creation for CN users will be disabled")
		return nil, err
	}
	return pointClient, nil
}

// 提供JWT管理器
func provideJWTManager(cfg *types.Config) *auth.Manager {
	jwtManager := auth.NewJWTManager(cfg.JWT.SecretKey, cfg.JWT.Expiration, "pxpat-user")
	return &jwtManager
}

// 提供服务层
func provideServices(
	userRepo repository.UserRepository,
	roleRepo repository.RoleRepository,
	verificationRepo repository.VerificationRepository,
	experienceRepo repository.ExperienceRepository,
	reputationRepo repository.ReputationRepository,
	followRepo repository.FollowRepository,
	blacklistRepo repository.BlacklistRepository,
	regionDetector region.RegionDetector,
	pointClient service.PointServiceClient,
	cfg *types.Config,
	jwtManager *auth.Manager,
) (*service.UserService, *service.RoleService, *service.LevelService, *service.FollowService, *service.BlacklistService) {
	log.Info().Msg("Storage client disabled for financial service integration testing")

	userService := service.NewUserService(userRepo, roleRepo, verificationRepo, regionDetector, pointClient, cfg, *jwtManager)
	roleService := service.NewRoleService(userRepo, roleRepo)
	levelService := service.NewLevelService(userRepo, experienceRepo, reputationRepo, roleService)
	followService := service.NewFollowService(followRepo, userRepo)
	blacklistService := service.NewBlacklistService(blacklistRepo, followRepo, userRepo)

	return userService, roleService, levelService, followService, blacklistService
}

// 提供别名服务
func provideAliasService(userRepo repository.UserRepository) *service.AliasService {
	aliasService := service.NewAliasService(userRepo)
	log.Info().Msg("Alias service initialized")
	return aliasService
}

// 提供内部服务层
func provideInternalServices(
	userRepo repository.UserRepository,
	roleRepo repository.RoleRepository,
	db *gorm.DB,
) (*intraService.InternalUserService, *intraService.InternalRoleService) {
	internalUserService := intraService.NewInternalUserService(userRepo, roleRepo, db)
	internalRoleService := intraService.NewInternalRoleService(roleRepo)
	log.Info().Msg("Internal services initialized")
	return internalUserService, internalRoleService
}

// 提供外部Handler层
func provideExternalHandlers(
	userService *service.UserService,
	roleService *service.RoleService,
	levelService *service.LevelService,
	aliasService *service.AliasService,
	followService *service.FollowService,
	blacklistService *service.BlacklistService,
	db *gorm.DB,
) (*handler.UserHandler, *handler.RoleHandler, *handler.LevelHandler, *handler.AliasHandler, *handler.FollowHandler, *handler.BlacklistHandler) {
	userHandler := handler.NewUserHandler(userService, db)
	roleHandler := handler.NewRoleHandler(roleService, userService)
	levelHandler := handler.NewLevelHandler(levelService)
	aliasHandler := handler.NewAliasHandler(aliasService)
	followHandler := handler.NewFollowHandler(followService)
	blacklistHandler := handler.NewBlacklistHandler(blacklistService)
	log.Info().Msg("External handlers initialized (using proxy services)")
	return userHandler, roleHandler, levelHandler, aliasHandler, followHandler, blacklistHandler
}

// 提供内部Handler层
func provideInternalHandlers(
	internalUserService *intraService.InternalUserService,
	internalRoleService *intraService.InternalRoleService,
	userService *service.UserService,
	roleService *service.RoleService,
	aliasService *service.AliasService,
	blacklistService *service.BlacklistService,
) (*intraHandler.InternalUserHandler, *intraHandler.InternalRoleHandler, *intraHandler.InternalAliasHandler, *intraHandler.InternalBlacklistHandler) {
	internalUserHandler := intraHandler.NewInternalUserHandler(internalUserService, userService)
	internalRoleHandler := intraHandler.NewInternalRoleHandler(roleService, internalRoleService)
	internalAliasHandler := intraHandler.NewInternalAliasHandler(aliasService, internalUserService)
	internalBlacklistHandler := intraHandler.NewInternalBlacklistHandler(blacklistService)
	log.Info().Msg("Internal handlers initialized")
	return internalUserHandler, internalRoleHandler, internalAliasHandler, internalBlacklistHandler
}

// 提供MQ多消费者
func provideMQMultiConsumer(cfg *types.Config, userRepo repository.UserRepository) *pkgMessaging.MultiConsumer {
	var multiConsumer *pkgMessaging.MultiConsumer
	if cfg.RabbitMQ.URL != "" {
		var err error
		multiConsumer, err = messaging.CreateUserServiceMultiConsumer(cfg.RabbitMQ.URL, userRepo)
		if err != nil {
			log.Warn().Err(err).Msg("Failed to initialize MQ multi consumer")
			log.Info().Msg("Continuing without MQ message consumption...")
		} else {
			log.Info().Msg("MQ multi consumer initialized successfully")
		}
	} else {
		log.Info().Msg("RabbitMQ URL not configured, skipping MQ consumer initialization")
	}
	return multiConsumer
}

// 提供Consul管理器
func provideConsulManager(cfg *types.Config) (*consul.Manager, error) {
	consulManager, err := consul.NewManager(&cfg.Consul)
	if err != nil {
		log.Fatal().Err(err).Msg("Consul管理器初始化失败")
		return nil, err
	}
	return consulManager, nil
}

// 提供健康检查处理器
func provideHealthHandler(consulManager *consul.Manager, db *gorm.DB) *consul.HealthHandler {
	healthHandler := consul.NewHealthHandler(consulManager, "1.0.0")

	// 添加数据库健康检查
	healthHandler.AddChecker(health.NewDatabaseHealthChecker("database", func() error {
		sqlDB, err := db.DB()
		if err != nil {
			return err
		}
		return sqlDB.Ping()
	}))

	return healthHandler
}

// 提供Gin引擎
func provideGinEngine(
	cfg *types.Config,
	userService *service.UserService,
	jwtManager *auth.Manager,
	userHandler *handler.UserHandler,
	roleHandler *handler.RoleHandler,
	levelHandler *handler.LevelHandler,
	aliasHandler *handler.AliasHandler,
	followHandler *handler.FollowHandler,
	blacklistHandler *handler.BlacklistHandler,
	internalUserHandler *intraHandler.InternalUserHandler,
	internalRoleHandler *intraHandler.InternalRoleHandler,
	internalAliasHandler *intraHandler.InternalAliasHandler,
	internalBlacklistHandler *intraHandler.InternalBlacklistHandler,
	healthHandler *consul.HealthHandler,
) *gin.Engine {
	// 创建HTTP路由器
	router := gin.Default()

	// 使用新的CORS中间件
	router.Use(pkgcors.CORSMiddleware(cfg.Security.CORS))

	// 注册服务路由
	routes.RegisterRoutes(
		router,
		(*jwtManager).(*auth.JWTManager),
		cfg,
		userHandler,
		roleHandler,
		levelHandler,
		aliasHandler,
		followHandler,
		blacklistHandler,
		internalUserHandler,
		internalRoleHandler,
		internalAliasHandler,
		internalBlacklistHandler,
	)

	// 用户服务既作为事件发布者，也消费用户存储事件
	if cfg.RabbitMQ.URL != "" {
		log.Info().Msg("RabbitMQ configured, user service ready to publish and consume events")
	} else {
		log.Info().Msg("RabbitMQ URL not configured, event publishing and consuming will be disabled")
	}

	// 注册健康检查路由
	healthHandler.RegisterHealthRoutes(router)

	return router
}

// 应用生命周期管理
func manageLifecycle(
	lc fx.Lifecycle,
	cfg *types.Config,
	consulManager *consul.Manager,
	mqMultiConsumer *pkgMessaging.MultiConsumer,
	ginEngine *gin.Engine,
) {
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			// 启动Consul管理器
			if err := consulManager.Start(ctx); err != nil {
				log.Fatal().Err(err).Msg("Consul管理器启动失败")
				return err
			}
			log.Info().Msg("Consul管理器启动成功")

			// 启动MQ多消费者（在goroutine中，使用新的context避免deadline问题）
			if mqMultiConsumer != nil {
				go func() {
					// 创建一个新的context，不继承fx OnStart的deadline
					consumerCtx := context.Background()
					log.Info().Msg("Starting MQ multi consumer")
					if err := mqMultiConsumer.StartConsuming(consumerCtx); err != nil {
						log.Error().Err(err).Msg("Error starting MQ multi consumer")
					}
				}()
			}

			// 启动HTTP服务器（在goroutine中）
			go func() {
				cmd.GraceStartAndClose(cfg.Server, ginEngine)
			}()

			return nil
		},
		OnStop: func(ctx context.Context) error {
			// 停止Consul管理器
			if err := consulManager.Stop(); err != nil {
				log.Error().Err(err).Msg("停止Consul管理器失败")
			} else {
				log.Info().Msg("Consul管理器停止成功")
			}

			// 关闭MQ多消费者
			if mqMultiConsumer != nil {
				if err := mqMultiConsumer.Close(); err != nil {
					log.Error().Err(err).Msg("关闭MQ多消费者失败")
				} else {
					log.Info().Msg("MQ多消费者关闭成功")
				}
			}

			return nil
		},
	})
}

func main() {
	app := fx.New(
		// 提供依赖
		fx.Provide(
			provideConfig,
			provideDatabase,
			provideRedis,
			provideRegionDetector,
			provideRepositories,
			providePointServiceClient,
			provideJWTManager,
			provideServices,
			provideAliasService,
			provideInternalServices,
			provideExternalHandlers,
			provideInternalHandlers,
			provideMQMultiConsumer,
			provideConsulManager,
			provideHealthHandler,
			provideGinEngine,
		),
		// 调用生命周期管理
		fx.Invoke(
			provideLogger,
			manageLifecycle,
		),
	)

	// 运行应用
	app.Run()
}
