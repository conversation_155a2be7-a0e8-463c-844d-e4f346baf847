package client

import (
	"fmt"
	"pxpat-backend/pkg/httpclient"
	"time"

	"pxpat-backend/internal/user-cluster/user-service/dto"

	"github.com/rs/zerolog/log"
)

// UserServiceClient 用户服务客户端接口
type UserServiceClient interface {
	// BatchGetUsers 根据KSUIDs获取用户信息
	BatchGetUsers(userKSUIDs []string) (*dto.GetUsersByKSUIDsResponse, error)
}

// userServiceClient 用户服务客户端实现
type userServiceClient struct {
	httpClient *httpclient.HTTPClient
}

// UserServiceConfig 用户服务客户端配置
type UserServiceConfig struct {
	BaseURL string
	Timeout time.Duration
}

// NewUserServiceClient 创建用户服务客户端
func NewUserServiceClient(config UserServiceConfig) UserServiceClient {
	httpClient := httpclient.NewHTTPClient(httpclient.ClientConfig{
		BaseURL:          config.BaseURL,
		Timeout:          config.Timeout,
		RetryCount:       3,
		RetryWaitTime:    1 * time.Second,
		RetryMaxWaitTime: 5 * time.Second,
	})
	return &userServiceClient{
		httpClient: httpClient,
	}
}

// ========= 本地特有的结构体定义 =========
// 注意：大部分结构体已迁移到user-service的dto包中，这里只保留audit-service特有的结构体

// BatchGetUsers 批量获取用户信息
func (c *userServiceClient) BatchGetUsers(userKSUIDs []string) (*dto.GetUsersByKSUIDsResponse, error) {
	log.Debug().
		Interface("user_ksuids", userKSUIDs).
		Msg("调用用户服务批量获取用户信息")

	var response httpclient.ServiceResponse[dto.GetUsersByKSUIDsResponse]

	// 构造请求
	request := dto.GetUsersByKSUIDsRequest{
		UserKSUIDs: userKSUIDs,
	}

	url := "/api/intra/users/batch-get-users"

	err := c.httpClient.Post(url, request, &response)
	if err != nil {
		log.Error().
			Err(err).
			Interface("user_ksuids", userKSUIDs).
			Str("url", url).
			Msg("调用用户服务批量获取用户信息API失败")
		return nil, fmt.Errorf("failed to call user service batch get users API: %w", err)
	}

	if !response.IsSuccess() {
		log.Error().
			Interface("user_ksuids", userKSUIDs).
			Str("error_message", response.GetErrorMessage()).
			Int("response_code", response.Code).
			Msg("用户服务批量获取用户信息返回错误")
		return nil, fmt.Errorf("user service returned error: %s", response.GetErrorMessage())
	}

	log.Info().
		Interface("user_ksuids", userKSUIDs).
		Int("found_count", response.Data.Total).
		Msg("用户服务批量获取用户信息成功")

	return &response.Data, nil
}
