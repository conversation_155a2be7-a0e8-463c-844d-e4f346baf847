package client

import (
	"fmt"
	"net/url"
	"pxpat-backend/pkg/httpclient"
	"time"

	"pxpat-backend/internal/user-cluster/user-service/dto"

	"github.com/rs/zerolog/log"
)

// UserServiceClient 用户服务客户端接口
type UserServiceClient interface {

	// GetUserByAlias 根据别名获取用户
	GetUserByAlias(aliasName string) (*dto.UserInfo, error)
	// CheckUserRole 检查用户是否有指定角色
	CheckUserRole(userKSUID, roleType string) (bool, error)
	// CreateUserRole 为用户创建角色
	CreateUserRole(userKSUID, roleType, region string) error
	// CreateUserAlias 为用户创建别名
	CreateUserAlias(userKSUID, aliasName string) error
	// SearchUsers 搜索用户
	SearchUsers(req *dto.SearchUsersRequest) (*dto.SearchUsersResponse, error)

	// 新增批量接口
	// BatchGetUsers 根据KSUIDs获取用户信息
	BatchGetUsers(userKSUIDs []string) (*dto.GetUsersByKSUIDsResponse, error)
	// BatchCheckUsers 批量检查用户是否存在
	BatchCheckUsers(req *dto.BatchCheckUsersRequest) (*dto.BatchCheckUsersResponse, error)
	// BatchCheckRoles 批量检查角色是否存在
	BatchCheckRoles(req *dto.BatchCheckRolesRequest) (*dto.BatchCheckRolesResponse, error)
	// BatchCreateRoles 批量创建角色
	BatchCreateRoles(req *dto.BatchCreateRolesRequest) (*dto.BatchCreateRolesResponse, error)
	// BatchCreateAlias 批量创建别名
	BatchCreateAlias(req *BatchCreateAliasRequest) (*BatchCreateAliasResponse, error)
	// BatchCreateUsers 批量创建用户
	BatchCreateUsers(req *BatchCreateUsersRequest) (*BatchCreateUsersResponse, error)
	// SimpleBatchCreateUsers 简单批量创建用户
	SimpleBatchCreateUsers(req *SimpleBatchCreateUsersRequest) (*SimpleBatchCreateUsersResponse, error)
	// GetUserFullInfo 获取用户完整信息（包含隐私设置）
	GetUserFullInfo(userKSUID string) (*dto.UserFullInfo, error)

	// 收藏相关方法
	// CheckFavoriteStatus 检查用户收藏状态 (已迁移到interaction-service)
	CheckFavoriteStatus(userKSUID, contentKSUID string) (bool, error)
	// GetContentFavoriteCount 获取内容收藏数 (已迁移到interaction-service)
	GetContentFavoriteCount(contentKSUID string) (int64, error)
}

// userServiceClient 用户服务客户端实现
type userServiceClient struct {
	httpClient *httpclient.HTTPClient
}

// UserServiceConfig 用户服务客户端配置
type UserServiceConfig struct {
	BaseURL string
	Timeout time.Duration
}

// NewUserServiceClient 创建用户服务客户端
func NewUserServiceClient(config UserServiceConfig) UserServiceClient {
	httpClient := httpclient.NewHTTPClient(httpclient.ClientConfig{
		BaseURL:          config.BaseURL,
		Timeout:          config.Timeout,
		RetryCount:       3,
		RetryWaitTime:    1 * time.Second,
		RetryMaxWaitTime: 5 * time.Second,
	})
	return &userServiceClient{
		httpClient: httpClient,
	}
}

// BatchCreateUsersRequest 批量创建用户请求
type BatchCreateUsersRequest struct {
	Users []BatchCreateUserInfo `json:"users"`
}

// BatchCreateUserInfo 批量创建用户信息
type BatchCreateUserInfo struct {
	Email     string   `json:"email"`
	Username  string   `json:"username,omitempty"`
	Nickname  string   `json:"nickname"`
	RealName  string   `json:"real_name,omitempty"`
	UserType  string   `json:"user_type"`
	Bio       string   `json:"bio,omitempty"`
	Region    string   `json:"region"`
	Roles     []string `json:"roles,omitempty"`
	UserKSUID string   `json:"user_ksuid,omitempty"`
}

// BatchCreateUsersResponse 批量创建用户响应
type BatchCreateUsersResponse struct {
	SuccessCount int                 `json:"success_count"`
	FailedCount  int                 `json:"failed_count"`
	Results      []BatchCreateResult `json:"results"`
	Message      string              `json:"message"`
}

// BatchCreateResult 批量创建结果
type BatchCreateResult struct {
	Email     string `json:"email"`
	UserKSUID string `json:"user_ksuid,omitempty"`
	Success   bool   `json:"success"`
	Error     string `json:"error,omitempty"`
	Action    string `json:"action,omitempty"`
}

// ========= 本地结构体定义 =========
// 为了保持微服务的独立性，在这里定义video-service需要的结构体

// SimpleBatchCreateUsersRequest 简单批量创建用户请求
type SimpleBatchCreateUsersRequest struct {
	Count int `json:"count"` // 要创建的用户数量
}

// SimpleBatchCreateUsersResponse 简单批量创建用户响应
type SimpleBatchCreateUsersResponse struct {
	UserKSUIDs []string `json:"user_ksuids"` // 创建的用户KSUID列表
	Total      int      `json:"total"`       // 成功创建的用户总数
}

// AliasCreateInfo 别名创建信息
type AliasCreateInfo struct {
	UserKSUID string `json:"user_ksuid"`
	AliasName string `json:"alias_name"`
}

// BatchCreateAliasRequest 批量创建别名请求
type BatchCreateAliasRequest struct {
	Aliases []AliasCreateInfo `json:"aliases"`
}

// AliasCreateResult 别名创建结果
type AliasCreateResult struct {
	UserKSUID string `json:"user_ksuid"`
	AliasName string `json:"alias_name"`
	Success   bool   `json:"success"`
	Error     string `json:"error,omitempty"`
}

// BatchCreateAliasResponse 批量创建别名响应
type BatchCreateAliasResponse struct {
	Results      []AliasCreateResult `json:"results"`
	SuccessCount int                 `json:"success_count"`
	FailedCount  int                 `json:"failed_count"`
}

// BatchCreateUsers 批量创建用户
func (c *userServiceClient) BatchCreateUsers(req *BatchCreateUsersRequest) (*BatchCreateUsersResponse, error) {
	log.Debug().
		Int("user_count", len(req.Users)).
		Msg("调用用户服务批量创建用户")

	var response httpclient.ServiceResponse[BatchCreateUsersResponse]

	url := "/api/intra/users/batch-create"

	err := c.httpClient.Post(url, req, &response)
	if err != nil {
		log.Error().
			Err(err).
			Str("url", url).
			Msg("调用用户服务批量创建用户API失败")
		return nil, fmt.Errorf("failed to call user service batch create API: %w", err)
	}

	if !response.IsSuccess() {
		log.Error().
			Str("error_message", response.GetErrorMessage()).
			Int("response_code", response.Code).
			Msg("用户服务批量创建用户返回错误")
		return nil, fmt.Errorf("user service returned error: %s", response.GetErrorMessage())
	}

	log.Info().
		Int("success_count", response.Data.SuccessCount).
		Int("failed_count", response.Data.FailedCount).
		Msg("用户服务批量创建用户成功")

	return &response.Data, nil
}

// GetUserByAlias 根据别名获取用户
func (c *userServiceClient) GetUserByAlias(aliasName string) (*dto.UserInfo, error) {
	log.Debug().
		Str("alias_name", aliasName).
		Msg("调用用户服务根据别名获取用户")

	var response httpclient.ServiceResponse[dto.UserInfo]

	// 使用POST方式传递别名，避免中文字符编码问题
	request := map[string]string{
		"alias_name": aliasName,
	}

	requestURL := "/api/intra/alias/query"

	err := c.httpClient.Post(requestURL, request, &response)
	if err != nil {
		log.Error().
			Err(err).
			Str("alias_name", aliasName).
			Str("url", requestURL).
			Msg("调用用户服务根据别名获取用户API失败")
		return nil, fmt.Errorf("failed to call user service get by alias API: %w", err)
	}

	if response.Code == 10032 {
		// 用户不存在，返回nil而不是错误
		return nil, nil
	}

	if !response.IsSuccess() {
		log.Error().
			Str("alias_name", aliasName).
			Str("error_message", response.GetErrorMessage()).
			Int("response_code", response.Code).
			Msg("用户服务根据别名获取用户返回错误")
		return nil, fmt.Errorf("user service returned error: %s", response.GetErrorMessage())
	}

	log.Info().
		Str("alias_name", aliasName).
		Str("user_ksuid", response.Data.UserKSUID).
		Msg("用户服务根据别名获取用户成功")

	return &response.Data, nil
}

// CheckUserRole 检查用户是否有指定角色
func (c *userServiceClient) CheckUserRole(userKSUID, roleType string) (bool, error) {
	log.Debug().
		Str("user_ksuid", userKSUID).
		Str("role_type", roleType).
		Msg("调用用户服务检查用户角色")

	var response httpclient.ServiceResponse[map[string]bool]

	url := fmt.Sprintf("/api/intra/users/%s/roles/%s/check", userKSUID, roleType)

	err := c.httpClient.Get(url, &response)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Str("role_type", roleType).
			Str("url", url).
			Msg("调用用户服务检查用户角色API失败")
		return false, fmt.Errorf("failed to call user service check role API: %w", err)
	}

	if !response.IsSuccess() {
		log.Error().
			Str("user_ksuid", userKSUID).
			Str("role_type", roleType).
			Str("error_message", response.GetErrorMessage()).
			Int("response_code", response.Code).
			Msg("用户服务检查用户角色返回错误")
		return false, fmt.Errorf("user service returned error: %s", response.GetErrorMessage())
	}

	hasRole := response.Data["has_role"]
	log.Info().
		Str("user_ksuid", userKSUID).
		Str("role_type", roleType).
		Bool("has_role", hasRole).
		Msg("用户服务检查用户角色成功")

	return hasRole, nil
}

// CreateUserRole 为用户创建角色
func (c *userServiceClient) CreateUserRole(userKSUID, roleType, region string) error {
	log.Debug().
		Str("user_ksuid", userKSUID).
		Str("role_type", roleType).
		Str("region", region).
		Msg("调用用户服务为用户创建角色")

	var response httpclient.ServiceResponse[interface{}]

	request := map[string]string{
		"user_ksuid": userKSUID,
		"role_type":  roleType,
		"region":     region,
	}

	url := "/api/intra/roles/create"

	err := c.httpClient.Post(url, request, &response)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Str("role_type", roleType).
			Str("region", region).
			Str("url", url).
			Msg("调用用户服务为用户创建角色API失败")
		return fmt.Errorf("failed to call user service create role API: %w", err)
	}

	if !response.IsSuccess() {
		log.Error().
			Str("user_ksuid", userKSUID).
			Str("role_type", roleType).
			Str("region", region).
			Str("error_message", response.GetErrorMessage()).
			Int("response_code", response.Code).
			Msg("用户服务为用户创建角色返回错误")
		return fmt.Errorf("user service returned error: %s", response.GetErrorMessage())
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("role_type", roleType).
		Str("region", region).
		Msg("用户服务为用户创建角色成功")

	return nil
}

// CreateUserAlias 为用户创建别名
func (c *userServiceClient) CreateUserAlias(userKSUID, aliasName string) error {
	log.Debug().
		Str("user_ksuid", userKSUID).
		Str("alias_name", aliasName).
		Msg("调用用户服务为用户创建别名")

	var response httpclient.ServiceResponse[interface{}]

	request := map[string]string{
		"user_ksuid": userKSUID,
		"alias_name": aliasName,
	}

	url := "/api/intra/alias/create"

	err := c.httpClient.Post(url, request, &response)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Str("alias_name", aliasName).
			Str("url", url).
			Msg("调用用户服务为用户创建别名API失败")
		return fmt.Errorf("failed to call user service create alias API: %w", err)
	}

	if !response.IsSuccess() {
		log.Error().
			Str("user_ksuid", userKSUID).
			Str("alias_name", aliasName).
			Str("error_message", response.GetErrorMessage()).
			Int("response_code", response.Code).
			Msg("用户服务为用户创建别名返回错误")
		return fmt.Errorf("user service returned error: %s", response.GetErrorMessage())
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("alias_name", aliasName).
		Msg("用户服务为用户创建别名成功")

	return nil
}

// BatchCheckUsers 批量检查用户是否存在
func (c *userServiceClient) BatchCheckUsers(req *dto.BatchCheckUsersRequest) (*dto.BatchCheckUsersResponse, error) {
	log.Debug().
		Int("user_count", len(req.Users)).
		Msg("调用用户服务批量检查用户")

	var response httpclient.ServiceResponse[dto.BatchCheckUsersResponse]

	url := "/api/intra/users/batch-check"

	err := c.httpClient.Post(url, req, &response)
	if err != nil {
		log.Error().
			Err(err).
			Str("url", url).
			Msg("调用用户服务批量检查用户API失败")
		return nil, fmt.Errorf("failed to call user service batch check users API: %w", err)
	}

	if !response.IsSuccess() {
		log.Error().
			Str("error_message", response.GetErrorMessage()).
			Int("response_code", response.Code).
			Msg("用户服务批量检查用户返回错误")
		return nil, fmt.Errorf("user service returned error: %s", response.GetErrorMessage())
	}

	log.Info().
		Int("user_count", len(req.Users)).
		Int("result_count", len(response.Data.Results)).
		Msg("用户服务批量检查用户成功")

	return &response.Data, nil
}

// BatchCheckRoles 批量检查角色是否存在
func (c *userServiceClient) BatchCheckRoles(req *dto.BatchCheckRolesRequest) (*dto.BatchCheckRolesResponse, error) {
	log.Debug().
		Int("role_count", len(req.Roles)).
		Msg("调用用户服务批量检查角色")

	var response httpclient.ServiceResponse[dto.BatchCheckRolesResponse]

	url := "/api/intra/roles/batch-check"

	err := c.httpClient.Post(url, req, &response)
	if err != nil {
		log.Error().
			Err(err).
			Str("url", url).
			Msg("调用用户服务批量检查角色API失败")
		return nil, fmt.Errorf("failed to call user service batch check roles API: %w", err)
	}

	if !response.IsSuccess() {
		log.Error().
			Str("error_message", response.GetErrorMessage()).
			Int("response_code", response.Code).
			Msg("用户服务批量检查角色返回错误")
		return nil, fmt.Errorf("user service returned error: %s", response.GetErrorMessage())
	}

	log.Info().
		Int("role_count", len(req.Roles)).
		Int("result_count", len(response.Data.Results)).
		Msg("用户服务批量检查角色成功")

	return &response.Data, nil
}

// BatchCreateRoles 批量创建角色
func (c *userServiceClient) BatchCreateRoles(req *dto.BatchCreateRolesRequest) (*dto.BatchCreateRolesResponse, error) {
	log.Debug().
		Int("role_count", len(req.Roles)).
		Msg("调用用户服务批量创建角色")

	var response httpclient.ServiceResponse[dto.BatchCreateRolesResponse]

	url := "/api/intra/roles/batch-create"

	err := c.httpClient.Post(url, req, &response)
	if err != nil {
		log.Error().
			Err(err).
			Str("url", url).
			Msg("调用用户服务批量创建角色API失败")
		return nil, fmt.Errorf("failed to call user service batch create roles API: %w", err)
	}

	if !response.IsSuccess() {
		log.Error().
			Str("error_message", response.GetErrorMessage()).
			Int("response_code", response.Code).
			Msg("用户服务批量创建角色返回错误")
		return nil, fmt.Errorf("user service returned error: %s", response.GetErrorMessage())
	}

	log.Info().
		Int("role_count", len(req.Roles)).
		Int("success_count", response.Data.SuccessCount).
		Int("failed_count", response.Data.FailedCount).
		Msg("用户服务批量创建角色成功")

	return &response.Data, nil
}

// BatchCreateAlias 批量创建别名
func (c *userServiceClient) BatchCreateAlias(req *BatchCreateAliasRequest) (*BatchCreateAliasResponse, error) {
	log.Debug().
		Int("alias_count", len(req.Aliases)).
		Msg("调用用户服务批量创建别名")

	var response httpclient.ServiceResponse[BatchCreateAliasResponse]

	url := "/api/intra/alias/batch-create"

	err := c.httpClient.Post(url, req, &response)
	if err != nil {
		log.Error().
			Err(err).
			Str("url", url).
			Msg("调用用户服务批量创建别名API失败")
		return nil, fmt.Errorf("failed to call user service batch create alias API: %w", err)
	}

	if !response.IsSuccess() {
		log.Error().
			Str("error_message", response.GetErrorMessage()).
			Int("response_code", response.Code).
			Msg("用户服务批量创建别名返回错误")
		return nil, fmt.Errorf("user service returned error: %s", response.GetErrorMessage())
	}

	log.Info().
		Int("alias_count", len(req.Aliases)).
		Int("success_count", response.Data.SuccessCount).
		Int("failed_count", response.Data.FailedCount).
		Msg("用户服务批量创建别名成功")

	return &response.Data, nil
}

func (c *userServiceClient) BatchGetUsers(userKSUIDs []string) (*dto.GetUsersByKSUIDsResponse, error) {
	log.Debug().
		Interface("user_ksuids", userKSUIDs).
		Msg("调用用户服务检查用户是否存在")

	var response httpclient.ServiceResponse[dto.GetUsersByKSUIDsResponse]

	// 构造请求，使用单个用户KSUID
	request := dto.GetUsersByKSUIDsRequest{
		UserKSUIDs: userKSUIDs,
	}

	url := "/api/intra/users/batch-get-users"

	err := c.httpClient.Post(url, request, &response)
	if err != nil {
		log.Error().
			Err(err).
			Interface("user_ksuids", userKSUIDs).
			Str("url", url).
			Msg("调用用户服务检查用户是否存在API失败")
		return nil, fmt.Errorf("failed to call user service check user exists API: %w", err)
	}

	if !response.IsSuccess() {
		log.Error().
			Interface("user_ksuid", userKSUIDs).
			Str("error_message", response.GetErrorMessage()).
			Int("response_code", response.Code).
			Msg("用户服务检查用户是否存在返回错误")
		return nil, fmt.Errorf("user service returned error: %s", response.GetErrorMessage())
	}

	// 检查返回的用户列表中是否包含该用户
	log.Info().
		Interface("user_ksuids", userKSUIDs).
		Interface("data", response.Data).
		Msg("用户服务检查用户是否存在成功")

	return &response.Data, nil
}

// SearchUsers 搜索用户
func (c *userServiceClient) SearchUsers(req *dto.SearchUsersRequest) (*dto.SearchUsersResponse, error) {
	log.Debug().
		Str("query", req.Query).
		Int("page", req.Page).
		Int("page_size", req.PageSize).
		Msg("调用用户服务搜索用户")

	var response httpclient.ServiceResponse[dto.SearchUsersResponse]

	// 构建URL和查询参数
	params := url.Values{}
	params.Add("query", req.Query)

	if req.Page > 0 {
		params.Add("page", fmt.Sprintf("%d", req.Page))
	}

	if req.PageSize > 0 {
		params.Add("page_size", fmt.Sprintf("%d", req.PageSize))
	}

	requestURL := "/api/users/search?" + params.Encode()

	err := c.httpClient.Get(requestURL, &response)
	if err != nil {
		log.Error().
			Err(err).
			Str("query", req.Query).
			Str("url", requestURL).
			Msg("调用用户服务搜索用户API失败")
		return nil, fmt.Errorf("failed to call user service search users API: %w", err)
	}

	if !response.IsSuccess() {
		log.Error().
			Str("query", req.Query).
			Str("error_message", response.GetErrorMessage()).
			Int("response_code", response.Code).
			Msg("用户服务搜索用户返回错误")
		return nil, fmt.Errorf("user service returned error: %s", response.GetErrorMessage())
	}

	log.Info().
		Str("query", req.Query).
		Int("page", req.Page).
		Int("page_size", req.PageSize).
		Int("result_count", len(response.Data.Users)).
		Int("total", response.Data.Total).
		Msg("用户服务搜索用户成功")

	return &response.Data, nil
}

// SimpleBatchCreateUsers 简单批量创建用户
func (c *userServiceClient) SimpleBatchCreateUsers(req *SimpleBatchCreateUsersRequest) (*SimpleBatchCreateUsersResponse, error) {
	log.Debug().
		Int("count", req.Count).
		Msg("调用用户服务简单批量创建用户")

	var response httpclient.ServiceResponse[SimpleBatchCreateUsersResponse]

	url := "/api/intra/users/simple-batch-create"

	err := c.httpClient.Post(url, req, &response)
	if err != nil {
		log.Error().
			Err(err).
			Str("url", url).
			Int("count", req.Count).
			Msg("调用用户服务简单批量创建用户API失败")
		return nil, fmt.Errorf("failed to call user service simple batch create API: %w", err)
	}

	if !response.IsSuccess() {
		log.Error().
			Str("error_message", response.GetErrorMessage()).
			Int("response_code", response.Code).
			Int("count", req.Count).
			Msg("用户服务简单批量创建用户返回错误")
		return nil, fmt.Errorf("user service returned error: %s", response.GetErrorMessage())
	}

	log.Info().
		Int("requested_count", req.Count).
		Int("success_count", response.Data.Total).
		Int("user_ksuids_count", len(response.Data.UserKSUIDs)).
		Msg("用户服务简单批量创建用户成功")

	return &response.Data, nil
}

// ========= 获取用户完整信息相关结构体 =========
// 注意：这些结构体已迁移到user-service的dto包中

// ========= 收藏相关结构体 (已迁移到interaction-service) =========
// 注意：这些类型定义已迁移到interaction_service_client.go中

// GetUserFullInfo 获取用户完整信息（包含隐私设置）
func (c *userServiceClient) GetUserFullInfo(userKSUID string) (*dto.UserFullInfo, error) {
	log.Debug().
		Str("user_ksuid", userKSUID).
		Msg("调用用户服务获取用户完整信息")

	var response httpclient.ServiceResponse[dto.GetUserFullInfoResponse]
	requestURL := "/api/intra/users/get-full-info"

	request := dto.GetUserFullInfoRequest{
		UserKSUID: userKSUID,
	}

	err := c.httpClient.Post(requestURL, request, &response)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Str("url", requestURL).
			Msg("调用用户服务获取用户完整信息API失败")
		return nil, fmt.Errorf("failed to call user service get full info API: %w", err)
	}

	if !response.IsSuccess() {
		log.Error().
			Str("user_ksuid", userKSUID).
			Str("url", requestURL).
			Str("error", response.GetErrorMessage()).
			Msg("用户服务获取用户完整信息返回错误")
		return nil, fmt.Errorf("user service returned error: %s", response.GetErrorMessage())
	}

	if response.Data.User == nil {
		log.Warn().
			Str("user_ksuid", userKSUID).
			Msg("用户服务返回空用户信息")
		return nil, fmt.Errorf("user not found")
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("nickname", response.Data.User.Nickname).
		Bool("secret_like", response.Data.User.SecretLike).
		Bool("secret_favorite", response.Data.User.SecretFavorite).
		Msg("用户服务获取用户完整信息成功")

	return response.Data.User, nil
}

// CheckFavoriteStatus 检查用户收藏状态 (已迁移到interaction-service)
// 注意：此方法已废弃，favorite功能已迁移到interaction-service
// 请使用interaction-service的相应接口
func (c *userServiceClient) CheckFavoriteStatus(userKSUID, contentKSUID string) (bool, error) {
	log.Warn().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", contentKSUID).
		Msg("调用已废弃的用户服务收藏状态检查API - favorite功能已迁移到interaction-service")

	// 返回false，不再调用user-service的favorite API
	// 调用方应该直接使用interaction-service的API
	return false, fmt.Errorf("favorite功能已迁移到interaction-service，请使用interaction-service的CheckFavoriteStatus接口")
}

// GetContentFavoriteCount 获取内容收藏数 (已迁移到interaction-service)
// 注意：此方法已废弃，favorite功能已迁移到interaction-service
// 请使用interaction-service的相应接口
func (c *userServiceClient) GetContentFavoriteCount(contentKSUID string) (int64, error) {
	log.Warn().
		Str("content_ksuid", contentKSUID).
		Msg("调用已废弃的用户服务获取内容收藏数API - favorite功能已迁移到interaction-service")

	// 返回0，不再调用user-service的favorite API
	// 调用方应该直接使用interaction-service的API
	return 0, fmt.Errorf("favorite功能已迁移到interaction-service，请使用interaction-service的GetContentFavoriteCount接口")
}
