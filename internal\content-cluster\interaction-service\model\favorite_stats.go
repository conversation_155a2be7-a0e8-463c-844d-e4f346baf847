package model

import (
	"time"

	"gorm.io/gorm"
	"pxpat-backend/pkg/ksuid"
)

// FavoriteStats 收藏统计表
type FavoriteStats struct {
	// 基础信息
	StatsKSUID   string      `gorm:"column:stats_ksuid;primaryKey;type:varchar(32);not null" json:"stats_ksuid"`
	ContentKSUID string      `gorm:"column:content_ksuid;type:varchar(32);not null;uniqueIndex:idx_content_ksuid" json:"content_ksuid"` // 内容ID，唯一索引
	ContentType  ContentType `gorm:"column:content_type;type:varchar(10);not null;index" json:"content_type"`                           // 内容类型

	// 统计数据
	FavoriteCount int64 `gorm:"column:favorite_count;type:bigint;not null;default:0;index" json:"favorite_count"` // 收藏数量（去重用户）

	// 基础字段
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
}

// TableName 返回表名
func (s *FavoriteStats) TableName() string {
	return "interaction_favorite_stats"
}

// BeforeCreate GORM钩子，在创建记录前自动生成KSUID
func (s *FavoriteStats) BeforeCreate(tx *gorm.DB) error {
	if s.StatsKSUID == "" {
		s.StatsKSUID = ksuid.GenerateKSUID()
	}
	return nil
}

// NewFavoriteStats 创建新的收藏统计记录
func NewFavoriteStats(contentKSUID string, contentType ContentType) *FavoriteStats {
	return &FavoriteStats{
		StatsKSUID:    ksuid.GenerateKSUID(),
		ContentKSUID:  contentKSUID,
		ContentType:   contentType,
		FavoriteCount: 0,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}
}

// IncrementFavorite 增加收藏数量
func (s *FavoriteStats) IncrementFavorite() {
	s.FavoriteCount++
	s.UpdatedAt = time.Now()
}

// DecrementFavorite 减少收藏数量
func (s *FavoriteStats) DecrementFavorite() {
	if s.FavoriteCount > 0 {
		s.FavoriteCount--
	}
	s.UpdatedAt = time.Now()
}

// UpdateCount 更新收藏数量
func (s *FavoriteStats) UpdateCount(favoriteCount int64) {
	s.FavoriteCount = favoriteCount
	s.UpdatedAt = time.Now()
}

// IsPopular 判断内容是否受欢迎（收藏数大于等于100）
func (s *FavoriteStats) IsPopular() bool {
	return s.FavoriteCount >= 100
}

// IsHot 判断内容是否热门（收藏数大于等于1000）
func (s *FavoriteStats) IsHot() bool {
	return s.FavoriteCount >= 1000
}
