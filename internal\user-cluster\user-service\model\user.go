package model

import (
	"pxpat-backend/pkg/ksuid"
	"time"
)

// 区域常量
const (
	RegionCN     = "CN"     // 中国大陆
	RegionGlobal = "GLOBAL" // 国际
)

// 用户状态常量
const (
	UserStatusActive    = "active"    // 活跃状态
	UserStatusInactive  = "inactive"  // 非活跃状态
	UserStatusSuspended = "suspended" // 封禁状态
)

// User 用户模型
type User struct {
	ID                int    `json:"id" gorm:"primaryKey;autoIncrement;type:bigserial"`                 // 自增主键ID, 主要用于内部关联
	UserKSUID         string `json:"user_ksuid" gorm:"column:user_ksuid;unique;not null;type:char(27)"` // 全局唯一的用户标识符 (KSUID格式), 用于API暴露和外部关联
	Email             string `json:"email" gorm:"unique;not null;type:varchar(255)"`                    // 用户邮箱, 用于登录、通知和找回密码, 唯一且不能为空
	Username          string `json:"username" gorm:"unique;default:null;type:varchar(64)"`              // 用户名, 唯一标识, 用于展示、@功能和个性化URL, 只能包含字母数字下划线
	Password          string `json:"-" gorm:"not null;type:varchar(255)"`                               // 加密后的密码 (通常使用带盐值的哈希算法如bcrypt), 不应在API中直接暴露
	Nickname          string `json:"nickname" gorm:"not null;type:varchar(64)"`                         // 用户昵称, 用于展示, 可以使用中文等多种字符, 非唯一
	UserType          string `json:"user_type" gorm:"default:'regular';type:varchar(32)"`               // 用户身份类型 (例如: regular, actor, director), 表示用户在现实世界中的身份或职业
	RealName          string `json:"real_name" gorm:"type:varchar(64)"`                                 // 真实姓名, 主要用于创作者、演员等需要展示真实身份的场景
	Bio               string `json:"bio" gorm:"type:text"`                                              // 个人简介, 一段关于用户的描述性文字
	AvatarURL         string `json:"avatar_url" gorm:"type:varchar(512)"`                               // 用户头像URL
	BannerURL         string `json:"banner_url" gorm:"type:varchar(512)"`                               // 用户横幅URL
	Level             int    `json:"level" gorm:"default:0;type:integer"`                               // 用户等级 (例如 0-10 对应 无等级到股东), 通常基于用户的注册时间或平台活跃度
	PersonalLevel     int    `json:"personal_level" gorm:"default:1;type:integer"`                      // 个人中心等级 (1-7对应丁级到至尊), 决定用户的积分奖励倍率
	Region            string `json:"region" gorm:"not null;default:'GLOBAL';type:varchar(16)"`          // 用户所属区域 (例如: CN 代表中国大陆, GLOBAL 代表国际), 用于区分不同区域的用户和服务
	Status            string `json:"status" gorm:"default:'active';type:varchar(16)"`                   // 用户状态 (例如: active 正常, inactive 未激活, suspended 封禁)
	Gender            string `json:"gender" gorm:"not null;default:'private';type:varchar(16)"`         // 用户性别 (例如: male 男性, female 女性, private 未知)
	Birthday          string `json:"birthday" gorm:"not null;default:'1970-01-01';type:varchar(20)"`    // 用户生日 (例如: 1990-01-01)
	LikeReceived      int64  `json:"like_received" gorm:"default:0;type:bigint"`                        // 接收到的赞数
	FollowersCount    int64  `json:"followers_count" gorm:"default:0;type:bigint"`                      // 粉丝人数
	FollowingCount    int64  `json:"following_count" gorm:"default:0;type:bigint"`                      // 关注人数
	HideComment       bool   `json:"hide_comment" gorm:"default:false;type:boolean"`                    // 是否隐藏评论功能（当用户评论被大量不喜欢时设置）
	SecretLike        bool   `json:"secret_like" gorm:"default:true;type:boolean"`                      // 是否隐藏点赞记录（默认隐藏）
	SecretFavorite    bool   `json:"secret_favorite" gorm:"default:true;type:boolean"`                  // 是否隐藏收藏记录（默认隐藏）
	IsFollowerPublic  bool   `json:"is_follower_public" gorm:"default:false;type:boolean"`              // 是否公开关注列表（默认不公开）
	IsFollowingPublic bool   `json:"is_following_public" gorm:"default:false;type:boolean"`             // 是否公开粉丝列表（默认不公开）

	// 一对多关系：用户别名
	UserAliases []UserAlias `json:"user_aliases" gorm:"foreignKey:UserKSUID;references:UserKSUID"` // 用户的别名列表

	// 能量系统相关字段
	Energy               float64   `json:"energy" gorm:"default:2;type:double precision"`               // 当前可用能量
	MaxEnergy            float64   `json:"max_energy" gorm:"default:2;type:double precision"`           // 最大能量值(基于用户等级)
	LastEnergyRegenTime  time.Time `json:"last_energy_regen_time" gorm:"type:timestamp"`                // 上次能量恢复时间
	EnergyRegenRate      float64   `json:"energy_regen_rate" gorm:"default:0.2;type:double precision"`  // 能量恢复速率(能量/小时)
	RewardMultiplier     float64   `json:"reward_multiplier" gorm:"default:0.25;type:double precision"` // 奖励倍率(基于个人中心等级)
	MinRewardRate        float64   `json:"min_reward_rate" gorm:"default:0.2;type:double precision"`    // 最低奖励率(20%)
	MaxRewardRate        float64   `json:"max_reward_rate" gorm:"default:0.3;type:double precision"`    // 最高奖励率(30%)
	DailyActionCount     int       `json:"daily_action_count" gorm:"default:0;type:integer"`            // 日常行动计数
	LastActionCountReset time.Time `json:"last_action_count_reset" gorm:"type:timestamp"`               // 上次行动计数重置时间

	IsSystemCreated bool       `json:"is_system_created" gorm:"default:false;type:boolean"` // 标记该用户账户是否由系统自动创建 (例如批量导入的偶像账户)
	IsClaimed       bool       `json:"is_claimed" gorm:"default:false;type:boolean"`        // 标记系统创建的偶像账户是否已被本人认领
	ClaimedAt       *time.Time `json:"claimed_at" gorm:"type:timestamp"`                    // 偶像账户被认领的具体时间
	ClaimedBy       string     `json:"claimed_by" gorm:"type:varchar(255)"`                 // 执行认领操作的联系方式或验证信息 (例如：认领者的邮箱或经纪人信息)
	CreatedByFan    string     `json:"created_by_fan" gorm:"type:char(27)"`                 // 如果该偶像账户是由粉丝创建的, 这里记录创建该账户的粉丝的用户ID (CreatorKSUID)
	RegisterIP      string     `json:"register_ip" gorm:"type:varchar(32)"`                 // 用户注册时使用的IP地址
	LastLoginIP     string     `json:"last_login_ip" gorm:"type:varchar(32)"`               // 用户最后一次登录时使用的IP地址
	LastLoginAt     time.Time  `json:"last_login_at" gorm:"type:timestamp"`                 // 用户最后一次登录的时间
	EmailVerified   bool       `json:"email_verified" gorm:"default:false;type:boolean"`    // 标记用户的邮箱地址是否已经过验证
	PhoneNumber     string     `json:"phone_number" gorm:"type:varchar(32)"`                // 用户的手机号码 (可选), 可用于登录或接收通知
	VerifiedName    string     `json:"verified_name" gorm:"type:varchar(64)"`               // 通过实名认证的真实姓名
	VerifiedIDCard  string     `json:"-" gorm:"type:varchar(32)"`                           // 通过实名认证的身份证号码 (敏感信息, 不应在API中直接暴露)
	IsVerified      bool       `json:"is_verified" gorm:"default:false;type:boolean"`       // 标记用户是否已通过实名认证
	InviteCode      string     `json:"invite_code" gorm:"unique;type:varchar(32)"`          // 用户专属的邀请码, 用于邀请其他用户注册, 通常是唯一的短字符串
	InvitedBy       string     `json:"invited_by" gorm:"type:varchar(32)"`                  // 邀请该用户注册的上级用户的邀请码
	CreatedAt       time.Time  `json:"created_at" gorm:"type:timestamp"`                    // 用户账户的创建时间
	UpdatedAt       time.Time  `json:"updated_at" gorm:"type:timestamp"`                    // 用户信息的最后更新时间
	DeletedAt       *time.Time `json:"deleted_at,omitempty" gorm:"index;type:timestamp"`    // 软删除时间标记, 如果不为null则表示用户已被软删除
}

// NewUser 创建新用户
func NewUser(email, password, nickname, region, invitedBy string) *User {
	// 如果未提供username，则使用nickname生成一个
	return &User{
		UserKSUID:  ksuid.GenerateKSUID(),
		Email:      email,
		Password:   password,
		Region:     region,
		Nickname:   nickname,
		UserType:   UserIdentityRegular, // 使用UserIdentity常量
		Level:      0,                   // 新用户默认无等级
		InvitedBy:  invitedBy,
		InviteCode: ksuid.GenerateKSUID()[:8],
		Status:     UserStatusActive,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}
}

// TableName 指定用户表名
func (*User) TableName() string {
	return "user_users"
}

// GetRegistrationDays 获取用户注册天数
func (u *User) GetRegistrationDays() int {
	return int(time.Since(u.CreatedAt).Hours() / 24)
}

// PerformAction 执行行动，消耗能量并计算奖励
func (u *User) PerformAction(actionType string, baseReward float64) (bool, float64) {
	var action EnergyAction

	// 映射旧的动作类型到新的EnergyAction类型
	switch actionType {
	case "content_create":
		action = EnergyActionPost
	case "content_share":
		action = EnergyActionShare
	case "content_audit":
		action = EnergyActionView
	case "daily_checkin":
		action = EnergyActionDailyTask
	default:
		action = EnergyActionView
	}

	// 使用新版ConsumeEnergy方法消耗能量
	success, _, _ := u.ConsumeEnergy(action)
	if !success {
		return false, 0.0
	}

	// 计算实际奖励
	actualReward := u.CalculateReward(baseReward)

	// 如果用户已经执行了很多行动，可以逐渐减少奖励
	if u.DailyActionCount > 10 {
		reductionFactor := 1.0 - float64(u.DailyActionCount-10)*0.05 // 每超过10次行动，奖励减少5%
		if reductionFactor < 0.5 {
			reductionFactor = 0.5 // 最低减至50%
		}
		actualReward *= reductionFactor
	}

	return true, actualReward
}

// IsIdol 判断是否为偶像类型用户
func (u *User) IsIdol() bool {
	idolTypes := []string{
		UserIdentityActor,
		UserIdentityDirector,
		UserIdentityProducer,
		UserIdentityPublisher,
		UserIdentitySinger,
		UserIdentityWriter,
		UserIdentityArtist,
	}

	for _, idolType := range idolTypes {
		if u.UserType == idolType {
			return true
		}
	}
	return false
}

// CanBeClaimed 判断账户是否可以被认领
func (u *User) CanBeClaimed() bool {
	return u.IsSystemCreated && !u.IsClaimed && u.IsIdol()
}

// ClaimAccount 认领账户
func (u *User) ClaimAccount(contactInfo string) {
	u.IsClaimed = true
	now := time.Now()
	u.ClaimedAt = &now
	u.ClaimedBy = contactInfo
	u.UpdatedAt = now
}

// GetIdolStats 获取偶像统计信息
func (u *User) GetIdolStats() map[string]interface{} {
	return map[string]interface{}{
		"follower_count":    u.FollowersCount,
		"following_count":   u.FollowingCount,
		"is_claimed":        u.IsClaimed,
		"created_by_fan":    u.CreatedByFan,
		"can_be_claimed":    u.CanBeClaimed(),
		"user_type_display": GetUserTypeDisplayName(u.UserType),
	}
}

// GetRewardRangeByPersonalLevel 获取指定个人中心等级的奖励率范围
func GetRewardRangeByPersonalLevel(personalLevel int) (float64, float64) {
	switch personalLevel {
	case PersonalLevelD:
		return 0.2, 0.3 // 20-30% 奖励
	case PersonalLevelC:
		return 0.3, 0.4 // 30-40% 奖励
	case PersonalLevelB:
		return 0.5, 0.6 // 50-60% 奖励
	case PersonalLevelA:
		return 0.9, 1.0 // 90-100% 奖励
	case PersonalLevelS10:
		return 1.0, 1.5 // 100-150% 奖励
	case PersonalLevelS20:
		return 2.0, 3.0 // 200-300% 奖励
	case PersonalLevelS:
		return 3.0, 5.0 // 300-500% 奖励
	default:
		return 0.2, 0.3 // 默认 20-30%
	}
}

// UpdateEnergyAndRates 更新用户的能量上限和奖励率
func (u *User) UpdateEnergyAndRates() {
	// 更新最大能量
	u.MaxEnergy = GetMaxEnergyByLevel(u.Level)

	// 更新奖励率范围
	minRate, maxRate := GetRewardRangeByPersonalLevel(u.PersonalLevel)
	u.MinRewardRate = minRate
	u.MaxRewardRate = maxRate

	// 设置当前奖励倍率为最低和最高的平均值
	u.RewardMultiplier = (minRate + maxRate) / 2

	// 确保当前能量不超过最大值
	if u.Energy > u.MaxEnergy {
		u.Energy = u.MaxEnergy
	}

	// 如果这是首次设置，初始化能量恢复时间
	if u.LastEnergyRegenTime.IsZero() {
		u.LastEnergyRegenTime = time.Now()
	}
}

// RegenerateEnergy 根据经过的时间重新计算用户的当前能量
func (u *User) RegenerateEnergy() {
	now := time.Now()

	// 如果能量已满，只更新时间戳
	if u.Energy >= u.MaxEnergy {
		u.LastEnergyRegenTime = now
		return
	}

	// 计算自上次更新以来经过的小时数
	hoursElapsed := now.Sub(u.LastEnergyRegenTime).Hours()

	// 计算恢复的能量
	energyToAdd := hoursElapsed * u.EnergyRegenRate

	// 更新当前能量，确保不超过最大值
	u.Energy += energyToAdd
	if u.Energy > u.MaxEnergy {
		u.Energy = u.MaxEnergy
	}

	// 更新时间戳
	u.LastEnergyRegenTime = now
}

// CalculateReward 计算给定基础奖励的实际奖励值
func (u *User) CalculateReward(baseReward float64) float64 {
	return baseReward * u.RewardMultiplier
}

// ResetDailyActionCount 重置每日行动计数（如果需要）
func (u *User) ResetDailyActionCount() {
	now := time.Now()

	// 如果从未重置或上次重置是昨天或更早
	if u.LastActionCountReset.IsZero() || now.YearDay() != u.LastActionCountReset.YearDay() || now.Year() != u.LastActionCountReset.Year() {
		u.DailyActionCount = 0
		u.LastActionCountReset = now
	}
}

// IncrementDailyActionCount 增加每日行动计数并返回更新后的值
func (u *User) IncrementDailyActionCount() int {
	// 先检查是否需要重置
	u.ResetDailyActionCount()

	// 增加计数
	u.DailyActionCount++

	return u.DailyActionCount
}

// HasEnoughEnergyForAction 检查用户是否有足够的能量执行指定行动
func (u *User) HasEnoughEnergyForAction(actionType string) bool {
	var action EnergyAction

	// 映射旧的动作类型到新的EnergyAction类型
	switch actionType {
	case "content_create":
		action = EnergyActionPost
	case "content_share":
		action = EnergyActionShare
	case "content_audit":
		action = EnergyActionView
	case "daily_checkin":
		action = EnergyActionDailyTask
	default:
		action = EnergyActionView
	}

	requiredEnergy := GetEnergyConsumption(action)

	// 重新生成能量
	u.RegenerateEnergy()

	return u.Energy >= requiredEnergy
}
