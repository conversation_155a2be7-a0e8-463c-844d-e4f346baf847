package repository

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"pxpat-backend/internal/content-cluster/video-service/client"
	"pxpat-backend/internal/content-cluster/video-service/model"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

// UserCreationRetryRepository 用户创建重试任务仓储
type UserCreationRetryRepository struct {
	db *gorm.DB
}

// NewUserCreationRetryRepository 创建用户创建重试任务仓储
func NewUserCreationRetryRepository(db *gorm.DB) *UserCreationRetryRepository {
	return &UserCreationRetryRepository{
		db: db,
	}
}

// UserCreationData 用户创建数据结构
type UserCreationData struct {
	PersonName string   `json:"person_name"`
	PersonType string   `json:"person_type"`
	Email      string   `json:"email"`
	Nickname   string   `json:"nickname"`
	RealName   string   `json:"real_name"`
	UserType   string   `json:"user_type"`
	Bio        string   `json:"bio"`
	Region     string   `json:"region"`
	Roles      []string `json:"roles"`
}

// CreateRetryTask 创建重试任务
func (r *UserCreationRetryRepository) CreateRetryTask(ctx context.Context, videoKSUID, personName, personType string, userInfo client.BatchCreateUserInfo, err error) error {
	log.Info().
		Str("video_ksuid", videoKSUID).
		Str("person_name", personName).
		Str("person_type", personType).
		Err(err).
		Msg("创建用户创建重试任务")

	// 构建用户创建数据
	userData := UserCreationData{
		PersonName: personName,
		PersonType: personType,
		Email:      userInfo.Email,
		Nickname:   userInfo.Nickname,
		RealName:   userInfo.RealName,
		UserType:   userInfo.UserType,
		Bio:        userInfo.Bio,
		Region:     userInfo.Region,
		Roles:      userInfo.Roles,
	}

	// 序列化用户数据
	userDataJSON, jsonErr := json.Marshal(userData)
	if jsonErr != nil {
		log.Error().
			Err(jsonErr).
			Str("person_name", personName).
			Msg("序列化用户创建数据失败")
		return fmt.Errorf("序列化用户创建数据失败: %w", jsonErr)
	}

	// 创建重试任务
	task := &model.UserCreationRetryTask{
		PersonName:       personName,
		PersonType:       personType,
		ContentKSUID:     videoKSUID,
		Status:           model.RetryTaskStatusPending,
		RetryCount:       0,
		MaxRetries:       5,
		NextRetryAt:      time.Now().Add(1 * time.Minute), // 1分钟后首次重试
		LastError:        err.Error(),
		ErrorHistory:     fmt.Sprintf(`[{"time":"%s","error":"%s"}]`, time.Now().Format(time.RFC3339), err.Error()),
		UserCreationData: string(userDataJSON),
	}

	// 保存到数据库
	if dbErr := r.db.WithContext(ctx).Create(task).Error; dbErr != nil {
		log.Error().
			Err(dbErr).
			Str("person_name", personName).
			Msg("保存重试任务到数据库失败")
		return fmt.Errorf("保存重试任务失败: %w", dbErr)
	}

	log.Info().
		Uint("task_id", task.ID).
		Str("person_name", personName).
		Time("next_retry_at", task.NextRetryAt).
		Msg("用户创建重试任务创建成功")

	return nil
}

// GetRetryableTasks 获取可重试的任务
func (r *UserCreationRetryRepository) GetRetryableTasks(ctx context.Context, limit int) ([]*model.UserCreationRetryTask, error) {
	var tasks []*model.UserCreationRetryTask

	err := r.db.WithContext(ctx).
		Where("status = ?", model.RetryTaskStatusPending).
		Where("retry_count < max_retries").
		Where("next_retry_at <= ?", time.Now()).
		Order("next_retry_at ASC").
		Limit(limit).
		Find(&tasks).Error

	if err != nil {
		log.Error().
			Err(err).
			Int("limit", limit).
			Msg("获取可重试任务失败")
		return nil, fmt.Errorf("获取可重试任务失败: %w", err)
	}

	log.Debug().
		Int("task_count", len(tasks)).
		Int("limit", limit).
		Msg("获取可重试任务成功")

	return tasks, nil
}

// UpdateTaskAfterRetry 重试后更新任务
func (r *UserCreationRetryRepository) UpdateTaskAfterRetry(ctx context.Context, task *model.UserCreationRetryTask, success bool, err error) error {
	if success {
		task.MarkSuccess()
		log.Info().
			Uint("task_id", task.ID).
			Str("person_name", task.PersonName).
			Msg("用户创建重试成功")
	} else {
		// 更新错误信息
		task.LastError = err.Error()

		// 添加到错误历史
		var errorHistory []map[string]string
		if task.ErrorHistory != "" {
			if jsonErr := json.Unmarshal([]byte(task.ErrorHistory), &errorHistory); jsonErr != nil {
				log.Warn().
					Err(jsonErr).
					Uint("task_id", task.ID).
					Msg("解析错误历史失败，重新初始化")
				errorHistory = []map[string]string{}
			}
		}

		errorHistory = append(errorHistory, map[string]string{
			"time":  time.Now().Format(time.RFC3339),
			"error": err.Error(),
		})

		if historyJSON, jsonErr := json.Marshal(errorHistory); jsonErr == nil {
			task.ErrorHistory = string(historyJSON)
		}

		// 增加重试次数并计算下次重试时间
		task.IncrementRetry()

		log.Warn().
			Uint("task_id", task.ID).
			Str("person_name", task.PersonName).
			Int("retry_count", task.RetryCount).
			Int("max_retries", task.MaxRetries).
			Time("next_retry_at", task.NextRetryAt).
			Str("status", string(task.Status)).
			Err(err).
			Msg("用户创建重试失败，已更新重试信息")
	}

	// 更新数据库
	if dbErr := r.db.WithContext(ctx).Save(task).Error; dbErr != nil {
		log.Error().
			Err(dbErr).
			Uint("task_id", task.ID).
			Msg("更新重试任务失败")
		return fmt.Errorf("更新重试任务失败: %w", dbErr)
	}

	return nil
}

// GetTaskStats 获取任务统计信息
func (r *UserCreationRetryRepository) GetTaskStats(ctx context.Context) (map[string]int64, error) {
	stats := make(map[string]int64)

	// 统计各状态的任务数量
	var result []struct {
		Status string
		Count  int64
	}

	err := r.db.WithContext(ctx).
		Model(&model.UserCreationRetryTask{}).
		Select("status, COUNT(*) as count").
		Group("status").
		Find(&result).Error

	if err != nil {
		log.Error().
			Err(err).
			Msg("获取任务统计信息失败")
		return nil, fmt.Errorf("获取任务统计信息失败: %w", err)
	}

	// 初始化所有状态为0
	stats["pending"] = 0
	stats["success"] = 0
	stats["failed"] = 0

	// 填充实际统计数据
	for _, r := range result {
		stats[r.Status] = r.Count
	}

	return stats, nil
}

// CleanupOldTasks 清理旧任务
func (r *UserCreationRetryRepository) CleanupOldTasks(ctx context.Context) error {
	now := time.Now()

	// 删除7天前的成功任务
	successCutoff := now.AddDate(0, 0, -7)
	successResult := r.db.WithContext(ctx).
		Where("status = ? AND updated_at < ?", model.RetryTaskStatusSuccess, successCutoff).
		Delete(&model.UserCreationRetryTask{})

	if successResult.Error != nil {
		log.Error().
			Err(successResult.Error).
			Time("cutoff", successCutoff).
			Msg("清理成功任务失败")
		return fmt.Errorf("清理成功任务失败: %w", successResult.Error)
	}

	// 删除30天前的失败任务
	failedCutoff := now.AddDate(0, 0, -30)
	failedResult := r.db.WithContext(ctx).
		Where("status = ? AND updated_at < ?", model.RetryTaskStatusFailed, failedCutoff).
		Delete(&model.UserCreationRetryTask{})

	if failedResult.Error != nil {
		log.Error().
			Err(failedResult.Error).
			Time("cutoff", failedCutoff).
			Msg("清理失败任务失败")
		return fmt.Errorf("清理失败任务失败: %w", failedResult.Error)
	}

	log.Info().
		Int64("success_deleted", successResult.RowsAffected).
		Int64("failed_deleted", failedResult.RowsAffected).
		Msg("旧任务清理完成")

	return nil
}
