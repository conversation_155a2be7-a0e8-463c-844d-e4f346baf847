package repository

import (
	"context"
	"fmt"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
	"pxpat-backend/internal/content-cluster/video-service/model"
)

type PublishStatsRepository struct {
	db *gorm.DB
}

func NewPublishStatsRepository(db *gorm.DB) *PublishStatsRepository {
	return &PublishStatsRepository{
		db: db,
	}
}

// UpdateCategoryStats 更新用户在指定分类的视频统计数 - 使用PostgreSQL JSONB原生操作
func (r *PublishStatsRepository) UpdateCategoryStats(ctx context.Context, userKSUID string, categoryID uint) error {
	log.Debug().
		Str("user_ksuid", userKSUID).
		Uint("category_id", categoryID).
		Msg("开始更新用户分类视频统计")

	categoryKey := fmt.Sprintf("%d", categoryID)

	// 使用两步操作：先更新content_json，再更新main_category
	// 第一步：更新或插入统计数据
	upsertSQL := `
		INSERT INTO video_publish_stats (user_ksuid, content_json, main_category, followers_count, created_at, updated_at)
		VALUES ($1, $2::jsonb, $3, 0, NOW(), NOW())
		ON CONFLICT (user_ksuid)
		DO UPDATE SET
			content_json = jsonb_set(
				video_publish_stats.content_json,
				$5,
				to_jsonb(
					COALESCE((video_publish_stats.content_json->>$4)::int, 0) + 1
				)
			),
			updated_at = NOW();
	`

	// 准备参数
	initialJSON := fmt.Sprintf(`{"%s": 1}`, categoryKey)
	categoryPath := fmt.Sprintf(`{%s}`, categoryKey)

	// 执行第一步：更新content_json
	err := r.db.WithContext(ctx).Exec(upsertSQL,
		userKSUID,    // $1: INSERT user_ksuid
		initialJSON,  // $2: INSERT/ELSE content_json (初始JSON和合并JSON)
		categoryID,   // $3: INSERT main_category (初始值，会被后续更新)
		categoryKey,  // $4: WHEN condition 和获取当前值的键
		categoryPath, // $5: jsonb_set path (JSONB路径)
	).Error

	if err != nil {
		return err
	}

	// 第二步：更新main_category为视频数量最多的分类
	updateMainCategorySQL := `
		UPDATE video_publish_stats
		SET main_category = (
			SELECT key::int
			FROM jsonb_each_text(content_json)
			ORDER BY value::int DESC
			LIMIT 1
		)
		WHERE user_ksuid = $1
	`

	err = r.db.WithContext(ctx).Exec(updateMainCategorySQL, userKSUID).Error

	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Uint("category_id", categoryID).
			Msg("更新用户分类视频统计失败")
		return err
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Uint("category_id", categoryID).
		Msg("更新用户分类视频统计成功")

	return nil
}

// UpdateFollowersCount 更新用户粉丝数量
func (r *PublishStatsRepository) UpdateFollowersCount(ctx context.Context, userKSUID string, followersCount int64) error {
	log.Debug().
		Str("user_ksuid", userKSUID).
		Int64("followers_count", followersCount).
		Msg("开始更新用户粉丝数量")

	// 使用UPSERT操作更新粉丝数量
	sql := `
		INSERT INTO video_publish_stats (user_ksuid, content_json, main_category, followers_count, created_at, updated_at)
		VALUES ($1, '{}'::jsonb, 0, $2, NOW(), NOW())
		ON CONFLICT (user_ksuid)
		DO UPDATE SET
			followers_count = $3,
			updated_at = NOW()
	`

	err := r.db.WithContext(ctx).Exec(sql, userKSUID, followersCount, followersCount).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Int64("followers_count", followersCount).
			Msg("更新用户粉丝数量失败")
		return err
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Int64("followers_count", followersCount).
		Msg("更新用户粉丝数量成功")

	return nil
}

// GetUsersByCategory 获取指定主分类的用户，按粉丝数倒序排列
func (r *PublishStatsRepository) GetUsersByCategory(ctx context.Context, categoryID uint, limit, offset int) ([]*model.PublishStats, int64, error) {
	log.Debug().
		Uint("category_id", categoryID).
		Int("limit", limit).
		Int("offset", offset).
		Msg("开始获取指定主分类的用户")

	// 获取总数
	var total int64

	err := r.db.WithContext(ctx).
		Model(&model.PublishStats{}).
		Where("main_category = ?", categoryID).
		Count(&total).Error
	if err != nil {
		log.Error().
			Err(err).
			Uint("category_id", categoryID).
			Msg("获取指定主分类用户总数失败")
		return nil, 0, err
	}

	// 获取分页数据，按粉丝数倒序排列
	var stats []*model.PublishStats
	err = r.db.WithContext(ctx).
		Model(&model.PublishStats{}).
		Where("main_category = ?", categoryID).
		Order("followers_count DESC, created_at ASC").
		Limit(limit).
		Offset(offset).
		Find(&stats).Error
	if err != nil {
		log.Error().
			Err(err).
			Uint("category_id", categoryID).
			Int("limit", limit).
			Int("offset", offset).
			Msg("获取指定主分类的用户失败")
		return nil, 0, err
	}

	log.Debug().
		Uint("category_id", categoryID).
		Int("limit", limit).
		Int("offset", offset).
		Int64("total", total).
		Int("count", len(stats)).
		Msg("获取指定主分类的用户成功")

	return stats, total, nil
}

// GetTopUsersByFollowers 获取按粉丝数排序的用户列表
func (r *PublishStatsRepository) GetTopUsersByFollowers(ctx context.Context, limit, offset int) ([]*model.PublishStats, int64, error) {
	log.Debug().
		Int("limit", limit).
		Int("offset", offset).
		Msg("开始获取按粉丝数排序的用户列表")

	// 获取总数
	var total int64
	err := r.db.WithContext(ctx).Model(&model.PublishStats{}).Count(&total).Error
	if err != nil {
		log.Error().
			Err(err).
			Msg("获取用户总数失败")
		return nil, 0, err
	}

	// 获取分页数据，按粉丝数倒序排列
	var stats []*model.PublishStats
	err = r.db.WithContext(ctx).
		Model(&model.PublishStats{}).
		Order("followers_count desc, created_at asc").
		Limit(limit).
		Offset(offset).
		Find(&stats).Error
	if err != nil {
		log.Error().
			Err(err).
			Int("limit", limit).
			Int("offset", offset).
			Msg("获取按粉丝数排序的用户列表失败")
		return nil, 0, err
	}

	log.Debug().
		Int("limit", limit).
		Int("offset", offset).
		Int64("total", total).
		Int("count", len(stats)).
		Msg("获取按粉丝数排序的用户列表成功")

	return stats, total, nil
}
