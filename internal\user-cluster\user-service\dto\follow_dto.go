package dto

import (
	"time"
)

// FollowUserRequest 关注用户请求
type FollowUserRequest struct {
	FolloweeKSUID string `json:"followee_ksuid" binding:"required" validate:"required"` // 被关注者的用户KSUID
}

// UnfollowUserRequest 取消关注用户请求
type UnfollowUserRequest struct {
	FolloweeKSUID string `json:"followee_ksuid" binding:"required" validate:"required"` // 被关注者的用户KSUID
}

// FollowUserResponse 关注用户响应
type FollowUserResponse struct {
	Success       bool   `json:"success"`        // 操作是否成功
	Message       string `json:"message"`        // 响应消息
	FolloweeKSUID string `json:"followee_ksuid"` // 被关注者的用户KSUID
	IsFollowing   bool   `json:"is_following"`   // 是否已关注
}

// UserFollowInfo 用户关注信息
type UserFollowInfo struct {
	UserKSUID      string    `json:"user_ksuid"`      // 用户KSUID
	Username       string    `json:"username"`        // 用户名
	Nickname       string    `json:"nickname"`        // 昵称
	AvatarURL      string    `json:"avatar_url"`      // 头像URL
	UserType       string    `json:"user_type"`       // 用户类型
	IsVerified     bool      `json:"is_verified"`     // 是否认证
	FollowedAt     time.Time `json:"followed_at"`     // 关注时间
	FollowingCount int64     `json:"following_count"` // 关注数
	FollowersCount int       `json:"followers_count"` // 粉丝数
}

// GetFollowersRequest 获取粉丝列表请求
type GetFollowersRequest struct {
	UserKSUID string `form:"user_ksuid" binding:"omitempty"`   // 用户KSUID，如果为空则获取当前用户的粉丝
	Page      int    `form:"page" binding:"min=1"`             // 页码，从1开始
	PageSize  int    `form:"page_size" binding:"min=1,max=50"` // 每页数量，最大100
}

// GetFollowingRequest 获取关注列表请求
type GetFollowingRequest struct {
	UserKSUID string `form:"user_ksuid"`                            // 用户KSUID，如果为空则获取当前用户的关注
	Page      int    `form:"page" binding:"min=1" validate:"min=1"` // 页码，从1开始
	PageSize  int    `form:"page_size" binding:"min=1,max=100"`     // 每页数量，最大100
}

// FollowListResponse 关注/粉丝列表响应
type FollowListResponse struct {
	Users      []UserFollowInfo `json:"users"`       // 用户列表
	Total      int64            `json:"total"`       // 总数
	Page       int              `json:"page"`        // 当前页码
	PageSize   int              `json:"page_size"`   // 每页数量
	TotalPages int              `json:"total_pages"` // 总页数
}

// CheckFollowStatusRequest 检查关注状态请求
type CheckFollowStatusRequest struct {
	FolloweeKSUID string `form:"followee_ksuid" binding:"required"` // 被关注者的用户KSUID
}

// CheckFollowStatusResponse 检查关注状态响应
type CheckFollowStatusResponse struct {
	IsFollowing    bool `json:"is_following"`     // 是否已关注
	IsFollowedBy   bool `json:"is_followed_by"`   // 是否被对方关注
	IsMutualFollow bool `json:"is_mutual_follow"` // 是否互相关注
}

// FollowStatsResponse 关注统计响应
type FollowStatsResponse struct {
	FollowingCount int64 `json:"following_count"` // 关注数
	FollowersCount int64 `json:"followers_count"` // 粉丝数
}

// BatchCheckFollowRequest 批量检查关注状态请求
type BatchCheckFollowRequest struct {
	UserKSUIDs []string `json:"user_ksuids" binding:"required,max=100" validate:"required,max=100"` // 用户KSUID列表，最多100个
}

// UserFollowStatus 用户关注状态
type UserFollowStatus struct {
	UserKSUID      string `json:"user_ksuid"`       // 用户KSUID
	IsFollowing    bool   `json:"is_following"`     // 是否已关注
	IsFollowedBy   bool   `json:"is_followed_by"`   // 是否被对方关注
	IsMutualFollow bool   `json:"is_mutual_follow"` // 是否互相关注
}

// BatchCheckFollowResponse 批量检查关注状态响应
type BatchCheckFollowResponse struct {
	FollowStatuses []UserFollowStatus `json:"follow_statuses"` // 关注状态列表
}

// BlockUserRequest 拉黑用户请求
type BlockUserRequest struct {
	BlockedKSUID string `json:"blocked_ksuid" binding:"required" validate:"required"` // 被拉黑者的用户KSUID
}

// UnblockUserRequest 取消拉黑用户请求
type UnblockUserRequest struct {
	BlockedKSUID string `json:"blocked_ksuid" binding:"required" validate:"required"` // 被拉黑者的用户KSUID
}

// BlockUserResponse 拉黑用户响应
type BlockUserResponse struct {
	Success      bool   `json:"success"`       // 操作是否成功
	Message      string `json:"message"`       // 响应消息
	BlockedKSUID string `json:"blocked_ksuid"` // 被拉黑者的用户KSUID
	IsBlocked    bool   `json:"is_blocked"`    // 是否已拉黑
}

// UserBlacklistInfo 用户黑名单信息
type UserBlacklistInfo struct {
	UserKSUID     string    `json:"user_ksuid"`     // 用户KSUID
	Username      string    `json:"username"`       // 用户名
	Nickname      string    `json:"nickname"`       // 昵称
	AvatarURL     string    `json:"avatar_url"`     // 头像URL
	UserType      string    `json:"user_type"`      // 用户类型
	IsVerified    bool      `json:"is_verified"`    // 是否认证
	BlacklistedAt time.Time `json:"blacklisted_at"` // 拉黑时间
}

// GetBlacklistRequest 获取黑名单列表请求
type GetBlacklistRequest struct {
	Page     int `form:"page" binding:"min=1"`             // 页码，从1开始
	PageSize int `form:"page_size" binding:"min=1,max=50"` // 每页数量，最大50
}

// BlacklistResponse 黑名单列表响应
type BlacklistResponse struct {
	Users      []UserBlacklistInfo `json:"users"`       // 用户列表
	Total      int64               `json:"total"`       // 总数
	Page       int                 `json:"page"`        // 当前页码
	PageSize   int                 `json:"page_size"`   // 每页数量
	TotalPages int                 `json:"total_pages"` // 总页数
}

// UserBlacklistStatus 用户黑名单状态
type UserBlacklistStatus struct {
	UserKSUID       string `json:"user_ksuid"`        // 用户KSUID
	IsBlacklisted   bool   `json:"is_blacklisted"`    // 是否已拉黑
	IsBlacklistedBy bool   `json:"is_blacklisted_by"` // 是否被对方拉黑
}

// CheckBlacklistStatusRequest 检查黑名单状态请求
type CheckBlacklistStatusRequest struct {
	TargetKSUID string `form:"target_ksuid" binding:"required"` // 目标用户的KSUID
}

// CheckBlacklistStatusResponse 检查黑名单状态响应
type CheckBlacklistStatusResponse struct {
	IsBlacklisted   bool `json:"is_blacklisted"`    // 是否已拉黑
	IsBlacklistedBy bool `json:"is_blacklisted_by"` // 是否被对方拉黑
}

// InternalCheckBlacklistStatusRequest 内部检查黑名单状态请求
type InternalCheckBlacklistStatusRequest struct {
	CurrentUserKSUID string `json:"current_user_ksuid" binding:"required"` // 当前用户KSUID
	TargetUserKSUID  string `json:"target_user_ksuid" binding:"required"`  // 目标用户KSUID
}
