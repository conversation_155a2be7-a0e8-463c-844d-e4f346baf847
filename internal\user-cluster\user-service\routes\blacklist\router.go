package blacklist

import (
	"github.com/gin-gonic/gin"
	"pxpat-backend/internal/user-cluster/user-service/external/handler"
	intraHandler "pxpat-backend/internal/user-cluster/user-service/intra/handler"
)

// RegisterBlacklistRoutes 注册黑名单功能的所有路由（外部和内部）
func RegisterBlacklistRoutes(r *gin.RouterGroup, blacklistHandler *handler.BlacklistHandler, internalBlacklistHandler *intraHandler.InternalBlacklistHandler, authMiddleware gin.HandlerFunc) {
	// 注册外部路由
	RegisterBlacklistExternalRoutes(r, blacklistHandler, authMiddleware)

	// 注册内部服务路由
	RegisterBlacklistInternalRoutes(r, internalBlacklistHandler)
}
