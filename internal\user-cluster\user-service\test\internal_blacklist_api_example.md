# 内部黑名单API使用示例

## 概述

本文档展示了如何使用内部黑名单API来检查两个用户之间的黑名单状态。这个API专门用于微服务间的内部调用，不需要认证。

## API端点

**POST** `/api/intra/blacklist/check-status`

## 请求格式

### 请求头
```
Content-Type: application/json
```

### 请求体
```json
{
  "current_user_ksuid": "2Zt8Ej9XYZ1234567890abcdef",
  "target_user_ksuid": "2Zt8Ej9XYZ0987654321fedcba"
}
```

### 参数说明
- `current_user_ksuid` (string, required): 当前用户的KSUID
- `target_user_ksuid` (string, required): 目标用户的KSUID

## 响应格式

### 成功响应 (200 OK)
```json
{
  "code": "SUCCESS",
  "message": "",
  "data": {
    "is_blacklisted": true,
    "is_blacklisted_by": false
  }
}
```

### 响应字段说明
- `is_blacklisted` (boolean): 当前用户是否已拉黑目标用户
- `is_blacklisted_by` (boolean): 当前用户是否被目标用户拉黑

### 错误响应 (400 Bad Request)
```json
{
  "code": "INVALID_PARAMETER",
  "message": "请求参数无效",
  "data": null
}
```

## 使用场景

1. **关注功能检查**: 在用户尝试关注其他用户时，检查是否存在黑名单关系
2. **内容推荐过滤**: 在推荐内容时，过滤掉被拉黑用户的内容
3. **消息发送验证**: 在发送私信前，检查用户间的黑名单状态
4. **评论权限控制**: 检查用户是否可以对特定内容进行评论

## cURL 示例

```bash
curl -X POST http://localhost:8080/api/intra/blacklist/check-status \
  -H "Content-Type: application/json" \
  -d '{
    "current_user_ksuid": "2Zt8Ej9XYZ1234567890abcdef",
    "target_user_ksuid": "2Zt8Ej9XYZ0987654321fedcba"
  }'
```

## 注意事项

1. 这是内部API，仅用于微服务间调用，不需要JWT认证
2. 两个KSUID参数都是必需的，不能为空
3. API会同时检查双向的黑名单关系
4. 响应时间通常在几毫秒内，适合高频调用

## 集成示例

### Go语言调用示例
```go
type CheckBlacklistRequest struct {
    CurrentUserKSUID string `json:"current_user_ksuid"`
    TargetUserKSUID  string `json:"target_user_ksuid"`
}

type CheckBlacklistResponse struct {
    IsBlacklisted   bool `json:"is_blacklisted"`
    IsBlacklistedBy bool `json:"is_blacklisted_by"`
}

func checkBlacklistStatus(currentUserKSUID, targetUserKSUID string) (*CheckBlacklistResponse, error) {
    reqBody := CheckBlacklistRequest{
        CurrentUserKSUID: currentUserKSUID,
        TargetUserKSUID:  targetUserKSUID,
    }
    
    jsonData, _ := json.Marshal(reqBody)
    
    resp, err := http.Post(
        "http://user-service:8080/api/intra/blacklist/check-status",
        "application/json",
        bytes.NewBuffer(jsonData),
    )
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()
    
    var result struct {
        Code string                     `json:"code"`
        Data *CheckBlacklistResponse    `json:"data"`
    }
    
    if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
        return nil, err
    }
    
    if result.Code != "SUCCESS" {
        return nil, fmt.Errorf("API调用失败: %s", result.Code)
    }
    
    return result.Data, nil
}
```
